"""
Test client cho SSE transport
"""

import asyncio
from fastmcp import Client

SERVER_URL = "http://127.0.0.1:8003/sse"

async def test_sse_server():
    """Test server với SSE transport"""
    print("🧪 Testing SSE Data Module MCP Server")
    print("=" * 50)
    print(f"Server URL: {SERVER_URL}")
    print()
    
    try:
        # Kết nối tới server
        print("1️⃣ Connecting to SSE server...")
        async with Client(SERVER_URL) as client:
            print("✅ Connected successfully!")
            
            # L<PERSON>y danh sách tools
            print("\n2️⃣ Getting tools list...")
            tools = await client.list_tools()
            print(f"✅ Found {len(tools)} tools:")
            
            for i, tool in enumerate(tools, 1):
                print(f"   {i}. {tool.name}")
            
            print("\n✅ SSE connection test successful!")
            
    except Exception as e:
        print(f"❌ Connection failed: {str(e)}")
        return False
    
    return True

def main():
    """Main function"""
    try:
        success = asyncio.run(test_sse_server())
        
        if success:
            print("\n🎉 SSE server test successful!")
            print("💡 Server is working with SSE transport!")
        else:
            print("\n❌ SSE server test failed!")
            
    except KeyboardInterrupt:
        print("\n⏹️ Test stopped by user")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
