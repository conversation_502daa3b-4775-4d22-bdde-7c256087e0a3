"""
Test client để kết nối tới Data Module MCP Server đang chạy
"""

import asyncio
from fastmcp import Client

SERVER_URL = "http://127.0.0.1:8002/mcp"

async def test_live_server():
    """Test server đang chạy"""
    print("🧪 Testing Live Data Module MCP Server")
    print("=" * 50)
    print(f"Server URL: {SERVER_URL}")
    print()
    
    try:
        # Kết nối tới server
        print("1️⃣ Connecting to server...")
        async with Client(SERVER_URL) as client:
            print("✅ Connected successfully!")
            
            # L<PERSON>y danh sách tools
            print("\n2️⃣ Getting tools list...")
            tools = await client.list_tools()
            print(f"✅ Found {len(tools)} tools:")
            
            for i, tool in enumerate(tools, 1):
                print(f"   {i}. {tool.name}")
                if hasattr(tool, 'description') and tool.description:
                    desc = tool.description.split('\n')[0]  # Chỉ lấy dòng đầu
                    desc = desc[:80] + "..." if len(desc) > 80 else desc
                    print(f"      {desc}")
            
            # Test authentication tools
            print("\n3️⃣ Testing authentication tools...")
            
            # Test check_auth_status
            try:
                print("   Testing check_auth_status...")
                auth_status = await client.call_tool("check_auth_status")
                print(f"   ✅ Result: {auth_status.text}")
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
            
            # Test update_bearer_token
            try:
                print("   Testing update_bearer_token...")
                update_result = await client.call_tool("update_bearer_token", {
                    "bearer_token": "test_token_demo_123"
                })
                print(f"   ✅ Result: {update_result.text}")
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
            
            # Test lại auth status sau khi update
            try:
                print("   Testing check_auth_status after update...")
                auth_status = await client.call_tool("check_auth_status")
                print(f"   ✅ Result: {auth_status.text}")
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
            
            print("\n4️⃣ Testing API tools (will fail without real API)...")
            
            # Test một số API tools
            api_tools = [tool for tool in tools if tool.name not in ['check_auth_status', 'update_bearer_token']]
            
            for tool in api_tools[:2]:  # Test 2 tools đầu tiên
                try:
                    print(f"   Testing {tool.name}...")
                    
                    # Thử với parameters minimal
                    if 'get' in tool.name.lower() and 'url' in tool.name.lower():
                        # Test get URLs với pagination
                        result = await client.call_tool(tool.name, {
                            "page": 1,
                            "limit": 5
                        })
                        print(f"   ✅ {tool.name}: {result.text[:100]}...")
                    elif 'get' in tool.name.lower() and 'media' in tool.name.lower():
                        # Test get media
                        result = await client.call_tool(tool.name, {
                            "page": 1,
                            "limit": 5
                        })
                        print(f"   ✅ {tool.name}: {result.text[:100]}...")
                    else:
                        print(f"   ⏭️ Skipping {tool.name} (requires specific parameters)")
                        
                except Exception as e:
                    error_msg = str(e)
                    if "401" in error_msg or "Unauthorized" in error_msg:
                        print(f"   ⚠️ {tool.name}: Authentication required (expected)")
                    elif "404" in error_msg or "Not Found" in error_msg:
                        print(f"   ⚠️ {tool.name}: API endpoint not found (expected)")
                    else:
                        print(f"   ❌ {tool.name}: {error_msg[:100]}...")
            
            print("\n✅ Live server test completed!")
            
    except Exception as e:
        print(f"❌ Connection failed: {str(e)}")
        return False
    
    return True

def main():
    """Main function"""
    try:
        success = asyncio.run(test_live_server())
        
        if success:
            print("\n🎉 Live server test successful!")
            print("💡 Server is working correctly!")
        else:
            print("\n❌ Live server test failed!")
            
    except KeyboardInterrupt:
        print("\n⏹️ Test stopped by user")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
