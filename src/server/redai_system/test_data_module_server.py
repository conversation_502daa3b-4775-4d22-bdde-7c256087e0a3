"""
Test script cho RedAI Data Module MCP Server

Script này kiểm tra các chức năng cơ bản của Data Module MCP Server
bao gồm khởi tạo server, load schema, và test các tools.
"""

import asyncio
import json
import sys
from pathlib import Path

# Thêm path để import modules
sys.path.append(str(Path(__file__).parent))

from fastmcp import Client
from data_module_server import data_module_server, load_openapi_schema
from data_module_config import (
    validate_pagination_params, 
    format_file_size,
    get_media_status_description,
    build_search_params
)

async def test_server_creation():
    """Test tạo server từ OpenAPI schema"""
    print("🧪 Testing server creation...")
    
    try:
        # Test load schema
        schema = load_openapi_schema()
        print(f"✅ Schema loaded successfully: {schema['info']['title']}")
        
        # Test create server
        mcp_server = data_module_server.create_server_with_auth()
        print(f"✅ Server created successfully: {mcp_server.name}")
        
        # Kiểm tra số lượng tools được tạo
        tools_count = len(mcp_server._tools)
        print(f"✅ Tools created: {tools_count} tools")
        
        return mcp_server
        
    except Exception as e:
        print(f"❌ Error creating server: {str(e)}")
        return None

async def test_tools_list(mcp_server):
    """Test liệt kê các tools có sẵn"""
    print("\n🧪 Testing tools listing...")
    
    try:
        # Sử dụng in-memory client để test
        async with Client(mcp_server) as client:
            tools = await client.list_tools()
            
            print(f"✅ Found {len(tools)} tools:")
            for tool in tools:
                print(f"   • {tool.name}: {tool.description[:100]}...")
                
            return tools
            
    except Exception as e:
        print(f"❌ Error listing tools: {str(e)}")
        return []

async def test_media_tools(mcp_server):
    """Test các tools liên quan đến media"""
    print("\n🧪 Testing media tools...")
    
    try:
        async with Client(mcp_server) as client:
            # Test get media list tool
            media_tools = [tool for tool in await client.list_tools() 
                          if "media" in tool.name.lower()]
            
            print(f"✅ Found {len(media_tools)} media tools:")
            for tool in media_tools:
                print(f"   • {tool.name}")
                
                # Test tool schema
                if hasattr(tool, 'inputSchema'):
                    schema = tool.inputSchema
                    properties = schema.get('properties', {})
                    print(f"     Parameters: {list(properties.keys())}")
                
    except Exception as e:
        print(f"❌ Error testing media tools: {str(e)}")

async def test_url_tools(mcp_server):
    """Test các tools liên quan đến URL"""
    print("\n🧪 Testing URL tools...")
    
    try:
        async with Client(mcp_server) as client:
            # Test get URL tools
            url_tools = [tool for tool in await client.list_tools() 
                        if "url" in tool.name.lower()]
            
            print(f"✅ Found {len(url_tools)} URL tools:")
            for tool in url_tools:
                print(f"   • {tool.name}")
                
                # Test tool schema
                if hasattr(tool, 'inputSchema'):
                    schema = tool.inputSchema
                    properties = schema.get('properties', {})
                    required = schema.get('required', [])
                    print(f"     Parameters: {list(properties.keys())}")
                    print(f"     Required: {required}")
                
    except Exception as e:
        print(f"❌ Error testing URL tools: {str(e)}")

async def test_statistics_tools(mcp_server):
    """Test các tools liên quan đến statistics"""
    print("\n🧪 Testing statistics tools...")
    
    try:
        async with Client(mcp_server) as client:
            # Test statistics tools
            stats_tools = [tool for tool in await client.list_tools() 
                          if "statistic" in tool.name.lower()]
            
            print(f"✅ Found {len(stats_tools)} statistics tools:")
            for tool in stats_tools:
                print(f"   • {tool.name}")
                
    except Exception as e:
        print(f"❌ Error testing statistics tools: {str(e)}")

def test_config_functions():
    """Test các utility functions trong config"""
    print("\n🧪 Testing config functions...")
    
    try:
        # Test pagination validation
        pagination = validate_pagination_params(page=0, limit=200)
        print(f"✅ Pagination validation: {pagination}")
        
        # Test file size formatting
        sizes = [0, 1024, 1048576, 1073741824]
        for size in sizes:
            formatted = format_file_size(size)
            print(f"✅ File size {size} bytes = {formatted}")
        
        # Test media status description
        statuses = ["DRAFT", "APPROVED", "PENDING", "REJECTED"]
        for status in statuses:
            desc = get_media_status_description(status)
            print(f"✅ Status {status}: {desc}")
        
        # Test search params building
        params = build_search_params(
            page=2,
            limit=20,
            search="test query",
            status="APPROVED",
            type="image"
        )
        print(f"✅ Search params: {params}")
        
    except Exception as e:
        print(f"❌ Error testing config functions: {str(e)}")

def test_schema_structure():
    """Test cấu trúc của OpenAPI schema"""
    print("\n🧪 Testing schema structure...")
    
    try:
        schema = load_openapi_schema()
        
        # Kiểm tra các phần chính của schema
        required_sections = ['openapi', 'info', 'paths', 'components']
        for section in required_sections:
            if section in schema:
                print(f"✅ Schema has {section} section")
            else:
                print(f"❌ Schema missing {section} section")
        
        # Kiểm tra endpoints
        paths = schema.get('paths', {})
        print(f"✅ Found {len(paths)} endpoints:")
        
        endpoint_methods = {}
        for path, methods in paths.items():
            method_list = list(methods.keys())
            endpoint_methods[path] = method_list
            print(f"   • {path}: {', '.join(method_list)}")
        
        # Kiểm tra components/schemas
        schemas = schema.get('components', {}).get('schemas', {})
        print(f"✅ Found {len(schemas)} schema definitions:")
        for schema_name in list(schemas.keys())[:5]:  # Chỉ hiển thị 5 đầu tiên
            print(f"   • {schema_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing schema structure: {str(e)}")
        return False

async def run_all_tests():
    """Chạy tất cả các tests"""
    print("🚀 Starting Data Module MCP Server Tests")
    print("=" * 60)
    
    # Test 1: Schema structure
    schema_ok = test_schema_structure()
    if not schema_ok:
        print("❌ Schema tests failed, stopping...")
        return
    
    # Test 2: Config functions
    test_config_functions()
    
    # Test 3: Server creation
    mcp_server = await test_server_creation()
    if not mcp_server:
        print("❌ Server creation failed, stopping...")
        return
    
    # Test 4: Tools listing
    tools = await test_tools_list(mcp_server)
    if not tools:
        print("❌ No tools found, stopping...")
        return
    
    # Test 5: Specific tool categories
    await test_media_tools(mcp_server)
    await test_url_tools(mcp_server)
    await test_statistics_tools(mcp_server)
    
    print("\n" + "=" * 60)
    print("✅ All tests completed successfully!")
    print("🎉 Data Module MCP Server is ready to use!")

def main():
    """Main function để chạy tests"""
    try:
        # Thiết lập encoding cho Windows
        if sys.platform == "win32":
            import os
            os.system("chcp 65001 > nul")
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        
        # Chạy async tests
        asyncio.run(run_all_tests())
        
    except KeyboardInterrupt:
        print("\n⏹️ Tests stopped by user")
    except Exception as e:
        print(f"❌ Error running tests: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
