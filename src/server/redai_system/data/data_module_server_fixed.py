"""
Server MCP cho RedAI Data Module API - <PERSON><PERSON><PERSON> bản sửa lỗi SSE

Server này sử dụng pure HTTP transport để tránh vấn đề 
"Not Acceptable: Client must accept text/event-stream"
"""

import os
import json
import httpx
from pathlib import Path
from typing import Optional, Dict, Any
import asyncio

# FastMCP imports
from fastmcp import FastMCP

# Cấu hình môi trường
API_BASE_URL = os.getenv("REDAI_DATA_API_BASE_URL", "https://api.redai.com")
HTTP_HOST = os.getenv("DATA_MODULE_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("DATA_MODULE_HTTP_PORT", "8002"))

def load_openapi_schema() -> dict:
    """
    Tải OpenAPI schema từ file swagger.json trong cùng thư mục
    """
    schema_path = Path(__file__).parent / "swagger.json"
    if not schema_path.exists():
        raise FileNotFoundError(f"Không tìm thấy file schema tại: {schema_path}")

    with open(schema_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def create_authenticated_client(base_url: str, bearer_token: Optional[str] = None) -> httpx.AsyncClient:
    """
    Tạo HTTP client với Bearer token authentication
    """
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    if bearer_token:
        headers["Authorization"] = f"Bearer {bearer_token}"
    
    return httpx.AsyncClient(
        base_url=base_url,
        headers=headers,
        timeout=30.0
    )

class FixedDataModuleServer:
    """
    Class để quản lý Data Module MCP Server với HTTP transport cố định
    """
    
    def __init__(self):
        self.openapi_spec = load_openapi_schema()
        self.base_url = API_BASE_URL
        self.current_token = None
        
    def create_server_with_auth(self, bearer_token: Optional[str] = None) -> FastMCP:
        """
        Tạo MCP server với Bearer token cụ thể
        """
        # Tạo authenticated HTTP client
        api_client = create_authenticated_client(self.base_url, bearer_token)
        
        # Tạo MCP server từ OpenAPI spec
        try:
            mcp = FastMCP.from_openapi(
                openapi_spec=self.openapi_spec,
                client=api_client,
                name="RedAI-Data-Module-Server-Fixed",
            )
        except TypeError:
            # Fallback cho phiên bản cũ hơn của FastMCP
            mcp = FastMCP.from_openapi(
                openapi_spec=self.openapi_spec,
                client=api_client,
            )
        
        # Thêm authentication tools
        self._add_auth_tools(mcp)
        
        return mcp
    
    def _add_auth_tools(self, mcp_server: FastMCP) -> None:
        """
        Thêm các tools để quản lý authentication
        """
        
        @mcp_server.tool()
        async def update_bearer_token(bearer_token: str) -> str:
            """
            Cập nhật Bearer token cho các API calls tiếp theo
            
            Args:
                bearer_token: Bearer token mới để xác thực
                
            Returns:
                Thông báo xác nhận việc cập nhật token
            """
            try:
                self.current_token = bearer_token
                return f"✅ Bearer token đã được cập nhật thành công."
                
            except Exception as e:
                return f"❌ Không thể cập nhật Bearer token: {str(e)}"
        
        @mcp_server.tool()
        async def check_auth_status() -> str:
            """
            Kiểm tra trạng thái authentication hiện tại
            
            Returns:
                Thông tin về trạng thái authentication
            """
            try:
                if self.current_token:
                    return "✅ Authentication đã được cấu hình và sẵn sàng sử dụng"
                else:
                    return "⚠️ Chưa có Bearer token. Sử dụng tool 'update_bearer_token' để cấu hình authentication"
                    
            except Exception as e:
                return f"❌ Không thể kiểm tra trạng thái authentication: {str(e)}"
        
        @mcp_server.tool()
        async def get_server_info() -> str:
            """
            Lấy thông tin về server và API endpoints
            
            Returns:
                Thông tin chi tiết về server
            """
            try:
                info = self.openapi_spec.get('info', {})
                paths = self.openapi_spec.get('paths', {})
                
                result = f"""🚀 RedAI Data Module MCP Server (Fixed)
📖 Title: {info.get('title', 'N/A')}
📝 Description: {info.get('description', 'N/A')}
🔢 Version: {info.get('version', 'N/A')}
🌐 API Base URL: {self.base_url}
📍 Endpoints: {len(paths)} endpoints
🔑 Current Token: {'✅ Set' if self.current_token else '❌ Not set'}

📋 Available Endpoints:"""
                
                for path, methods in paths.items():
                    method_list = list(methods.keys())
                    first_method = list(methods.values())[0]
                    tags = first_method.get("tags", [])
                    tag_str = f" [{', '.join(tags)}]" if tags else ""
                    result += f"\n   • {path} [{', '.join(method.upper() for method in method_list)}]{tag_str}"
                
                return result
                
            except Exception as e:
                return f"❌ Không thể lấy thông tin server: {str(e)}"

# Tạo instance của FixedDataModuleServer
fixed_server = FixedDataModuleServer()

def create_fixed_server() -> FastMCP:
    """
    Tạo server đã sửa lỗi
    """
    return fixed_server.create_server_with_auth()

def print_server_info():
    """
    In thông tin về server
    """
    try:
        schema = fixed_server.openapi_spec
        endpoints = list(schema.get("paths", {}).keys())
        
        print("="*70)
        print("🚀 RedAI Data Module MCP Server (Fixed)")
        print("="*70)
        print("📋 Cấu hình:")
        print(f"   🌐 API Base URL: {API_BASE_URL}")
        print(f"   🔑 Authentication: Nhận từ client")
        print(f"   🚀 Transport: STDIO (tránh lỗi SSE)")
        print(f"   🌐 Server Host: {HTTP_HOST}:{HTTP_PORT}")
        print()
        
        print(f"📊 API Information:")
        print(f"   📖 Title: {schema.get('info', {}).get('title', 'N/A')}")
        print(f"   📝 Description: {schema.get('info', {}).get('description', 'N/A')}")
        print(f"   🔢 Version: {schema.get('info', {}).get('version', 'N/A')}")
        print(f"   📍 Endpoints: {len(endpoints)} endpoints")
        print()
        
        print("📋 Available Endpoints:")
        for path in endpoints:
            methods = list(schema["paths"][path].keys())
            first_method = list(schema["paths"][path].values())[0]
            tags = first_method.get("tags", [])
            tag_str = f" [{', '.join(tags)}]" if tags else ""
            print(f"   • {path} [{', '.join(method.upper() for method in methods)}]{tag_str}")
        
        print()
        print("🔧 Built-in Tools:")
        print("   • update_bearer_token: Cập nhật Bearer token")
        print("   • check_auth_status: Kiểm tra trạng thái authentication")
        print("   • get_server_info: Lấy thông tin server chi tiết")
        
        print()
        print("💡 Lưu ý:")
        print("   • Server sử dụng STDIO transport để tránh lỗi SSE")
        print("   • Kết nối qua command line hoặc process communication")
        print("   • Không cần HTTP client, sử dụng trực tiếp với FastMCP")
        
        print("="*70)
        
    except Exception as e:
        print(f"❌ Lỗi khi tải thông tin server: {str(e)}")

def run_stdio_server():
    """
    Chạy server với STDIO transport để tránh lỗi HTTP/SSE
    """
    try:
        print("🚀 Khởi động server với STDIO transport...")
        print("💡 Server sẽ giao tiếp qua stdin/stdout")
        print("💡 Sử dụng tool 'get_server_info' để xem thông tin chi tiết")
        print()
        
        # Tạo và chạy server
        mcp = create_fixed_server()
        mcp.run(transport="stdio")
        
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server: {str(e)}")
        raise

def run_http_server_alternative():
    """
    Chạy server với HTTP transport thay thế (nếu cần)
    """
    try:
        print(f"🚀 Khởi động server với HTTP transport tại http://{HTTP_HOST}:{HTTP_PORT}")
        
        # Tạo server
        mcp = create_fixed_server()
        
        # Thử chạy với HTTP transport đơn giản
        mcp.run(
            transport="http",
            host=HTTP_HOST,
            port=HTTP_PORT
        )
        
    except Exception as e:
        print(f"❌ Lỗi với HTTP transport: {str(e)}")
        print("🔄 Chuyển sang STDIO transport...")
        run_stdio_server()

def main():
    """
    Hàm main để khởi chạy MCP server
    """
    try:
        # Thiết lập encoding cho Windows console
        import sys
        if sys.platform == "win32":
            import os
            os.system("chcp 65001 > nul")  # Set UTF-8 encoding
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        
        # In thông tin server
        print_server_info()
        
        # Kiểm tra command line arguments
        import sys
        transport_mode = "stdio"  # Mặc định dùng STDIO để tránh lỗi
        
        if len(sys.argv) > 1:
            if sys.argv[1] == "http":
                transport_mode = "http"
            elif sys.argv[1] == "stdio":
                transport_mode = "stdio"
        
        if transport_mode == "http":
            run_http_server_alternative()
        else:
            run_stdio_server()
        
    except KeyboardInterrupt:
        print("\n⏹️  Server đã được dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
