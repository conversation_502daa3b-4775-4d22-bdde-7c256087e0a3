# RedAI Data Module MCP Server

Server MCP cho RedAI Data Module API sử dụng FastMCP framework. Server này tự động tạo các MCP tools và resources từ OpenAPI specification trong file `swagger.json`.

## Tính năng

- 🔧 **Tự động tạo tools** từ OpenAPI specification
- 🔐 **Bearer token authentication** 
- 🌐 **HTTP transport** với FastMCP
- 📝 **Tùy chỉnh component names** dễ hiểu
- 🎯 **Error handling** và logging chi tiết
- 📊 **Thống kê và monitoring** built-in

## Cấu trúc API

### Media Endpoints (🖼️ User Media)
- `get_my_media_list` - Lấy danh sách media của user hiện tại
- `delete_my_media_batch` - Xóa nhiều media cùng lúc

### URL Endpoints (🔗 User URL)  
- `get_my_urls` - Lấy danh sách URLs của user
- `create_new_url` - Tạo URL mới
- `get_url_details` - L<PERSON>y chi tiết URL theo ID
- `update_url` - Cập nhật thông tin URL
- `delete_url` - Xóa URL

### Statistics Endpoints (📊 User Statistics)
- `get_user_statistics` - Lấy thống kê tổng quan của user
- `get_storage_statistics` - Lấy thống kê dung lượng lưu trữ

## Cài đặt và Cấu hình

### 1. Cài đặt Dependencies

```bash
pip install fastmcp httpx
```

### 2. Cấu hình Environment Variables

```bash
# API Configuration
export REDAI_DATA_API_BASE_URL="https://api.redai.com"

# Server Configuration
export DATA_MODULE_HTTP_HOST="127.0.0.1"
export DATA_MODULE_HTTP_PORT="8002"
export DATA_MODULE_HTTP_PATH="/mcp"
```

**Lưu ý**: Bearer token sẽ được truyền từ client khi kết nối, không cần cấu hình trong environment variables.

### 3. Chạy Server

```bash
# Chạy trực tiếp
python data_module_server.py

# Hoặc import và sử dụng
from data_module_server import mcp
mcp.run(transport="streamable-http", host="127.0.0.1", port=8002)
```

## Sử dụng với MCP Client

### Kết nối qua HTTP Transport

```python
from fastmcp import Client

async def main():
    # Kết nối tới Data Module MCP Server
    async with Client("http://127.0.0.1:8002/mcp") as client:
        # Bước 1: Cấu hình Bearer token
        auth_result = await client.call_tool("update_bearer_token", {
            "bearer_token": "your_bearer_token_here"
        })
        print("Auth setup:", auth_result.text)

        # Bước 2: Kiểm tra trạng thái authentication
        auth_status = await client.call_tool("check_auth_status")
        print("Auth status:", auth_status.text)

        # Bước 3: Sử dụng các API tools
        # Lấy danh sách tools
        tools = await client.list_tools()
        print("Available tools:", [tool.name for tool in tools])

        # Gọi tool để lấy media list
        result = await client.call_tool("get_my_media_list", {
            "page": 1,
            "limit": 10,
            "search": "image"
        })
        print("Media list:", result.text)

        # Tạo URL mới
        new_url = await client.call_tool("create_new_url", {
            "url": "https://example.com/article",
            "title": "Bài viết hay",
            "content": "Nội dung bài viết...",
            "tags": ["tutorial", "example"]
        })
        print("Created URL:", new_url.text)
```

### Cấu hình MCP Client

```json
{
  "mcpServers": {
    "redai-data": {
      "url": "http://127.0.0.1:8002/mcp",
      "description": "RedAI Data Module API"
    }
  }
}
```

## Authentication từ Client

Server này được thiết kế để nhận Bearer token từ client thay vì cấu hình trong environment variables. Điều này cho phép:

- **Bảo mật cao hơn**: Token không được lưu trữ trong server
- **Linh hoạt**: Mỗi client có thể sử dụng token khác nhau
- **Dynamic**: Có thể thay đổi token trong quá trình sử dụng

### Cách sử dụng Authentication

1. **Kết nối tới server** mà không cần token
2. **Sử dụng tool `update_bearer_token`** để cấu hình authentication
3. **Kiểm tra trạng thái** bằng tool `check_auth_status`
4. **Sử dụng các API tools** bình thường

```python
# Ví dụ đầy đủ
async with Client("http://127.0.0.1:8002/mcp") as client:
    # Cấu hình authentication
    await client.call_tool("update_bearer_token", {
        "bearer_token": "your_actual_bearer_token"
    })

    # Kiểm tra trạng thái
    status = await client.call_tool("check_auth_status")
    print(status.text)

    # Sử dụng API
    media = await client.call_tool("get_my_media_list", {"page": 1})
```

## API Examples

### 1. Quản lý Media

```python
# Lấy danh sách media với filter
media_list = await client.call_tool("get_my_media_list", {
    "page": 1,
    "limit": 20,
    "search": "beautiful image",
    "status": "APPROVED"
})

# Xóa nhiều media
delete_result = await client.call_tool("delete_my_media_batch", {
    "mediaIds": [
        "123e4567-e89b-12d3-a456-426614174000",
        "456e7890-e89b-12d3-a456-426614174001"
    ]
})
```

### 2. Quản lý URLs

```python
# Lấy danh sách URLs với sorting
urls = await client.call_tool("get_my_urls", {
    "page": 1,
    "limit": 10,
    "keyword": "tutorial",
    "type": "web",
    "tags": ["nestjs", "backend"],
    "sortBy": "createdAt",
    "sortDirection": "DESC"
})

# Cập nhật URL
updated_url = await client.call_tool("update_url", {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "title": "Updated Title",
    "content": "Updated content...",
    "tags": ["updated", "tutorial"]
})
```

### 3. Thống kê

```python
# Lấy thống kê tổng quan
stats = await client.call_tool("get_user_statistics")

# Lấy thống kê dung lượng
storage_stats = await client.call_tool("get_storage_statistics")
```

## Error Handling

Server tự động xử lý các lỗi phổ biến:

- **400 Bad Request**: Dữ liệu đầu vào không hợp lệ
- **401 Unauthorized**: Token không hợp lệ hoặc hết hạn
- **403 Forbidden**: Không có quyền truy cập
- **404 Not Found**: Tài nguyên không tồn tại
- **500 Internal Server Error**: Lỗi máy chủ

## Monitoring và Logging

Server cung cấp thông tin chi tiết về:
- Số lượng endpoints được load
- Trạng thái authentication
- Performance metrics
- Error tracking

## Troubleshooting

### 1. Lỗi Authentication
```
❌ 401 Unauthorized
```
**Giải pháp**: Kiểm tra `REDAI_BEARER_TOKEN` environment variable

### 2. Lỗi Connection
```
❌ Connection refused
```
**Giải pháp**: Kiểm tra `REDAI_DATA_API_BASE_URL` và network connectivity

### 3. Lỗi Schema Loading
```
❌ FileNotFoundError: swagger.json
```
**Giải pháp**: Đảm bảo file `data/swagger.json` tồn tại và có quyền đọc

## Development

### Cấu trúc Files
```
src/server/redai_system/
├── data_module_server.py      # Server chính
├── data_module_config.py      # Cấu hình và utilities
├── data/
│   ├── swagger.json          # OpenAPI specification
│   └── README.md            # Documentation này
```

### Customization

Để tùy chỉnh server, chỉnh sửa các functions trong `data_module_server.py`:

- `customize_component_names()` - Đổi tên tools/resources
- `get_endpoint_description()` - Tùy chỉnh mô tả endpoints
- `customize_mcp_components()` - Thêm metadata và behaviors

## License

Phần mềm này thuộc về RedAI Development Team.
