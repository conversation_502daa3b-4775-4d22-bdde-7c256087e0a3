"""
<PERSON><PERSON><PERSON> hình cho RedAI Data Module MCP Server

File này chứa các cấu hình, constants và utility functions
để hỗ trợ Data Module MCP Server.
"""

import os
from typing import Dict, Any, Optional
from enum import Enum

class MediaStatus(Enum):
    """Enum cho trạng thái media"""
    DRAFT = "DRAFT"
    APPROVED = "APPROVED" 
    PENDING = "PENDING"
    REJECTED = "REJECTED"

class OwnerType(Enum):
    """Enum cho loại chủ sở hữu"""
    USER = "USER"
    ADMIN = "ADMIN"

class SortDirection(Enum):
    """Enum cho hướng sắp xếp"""
    ASC = "ASC"
    DESC = "DESC"

class URLSortBy(Enum):
    """Enum cho trường sắp xếp URL"""
    TITLE = "title"
    CREATED_AT = "createdAt"
    UPDATED_AT = "updatedAt"

# Cấu hình mặc định
DEFAULT_CONFIG = {
    "api_base_url": "https://api.redai.com",
    "http_host": "127.0.0.1",
    "http_port": 8002,
    "http_path": "/mcp",
    "timeout": 30.0,
    "max_retries": 3,
    "pagination": {
        "default_page": 1,
        "default_limit": 10,
        "max_limit": 100
    }
}

# Mapping cho tên tools dễ hiểu
TOOL_NAME_MAPPING = {
    # Media tools
    "get_media_my_media": "get_my_media_list",
    "delete_media_my_media": "delete_my_media_batch",
    
    # URL tools
    "get_data_url": "get_my_urls", 
    "post_data_url": "create_new_url",
    "get_data_url_by_id": "get_url_details",
    "put_data_url_by_id": "update_url",
    "delete_data_url_by_id": "delete_url",
    
    # Statistics tools
    "get_statistics": "get_user_statistics",
    "get_statistics_storage": "get_storage_statistics",
}

# Emoji mapping cho các loại endpoint
ENDPOINT_EMOJIS = {
    "User Media": "🖼️",
    "User URL": "🔗",
    "User Statistics": "📊",
    "Admin Media": "👑🖼️",
    "Admin URL": "👑🔗", 
    "Admin Statistics": "👑📊"
}

def get_config_value(key: str, default: Any = None) -> Any:
    """
    Lấy giá trị cấu hình từ environment variable hoặc default config
    
    Args:
        key: Tên key cấu hình
        default: Giá trị mặc định nếu không tìm thấy
        
    Returns:
        Giá trị cấu hình
    """
    # Chuyển key thành env var name (snake_case -> UPPER_CASE)
    env_key = f"REDAI_DATA_{key.upper()}"
    
    # Thử lấy từ environment variable trước
    env_value = os.getenv(env_key)
    if env_value is not None:
        # Thử convert sang int nếu có thể
        try:
            return int(env_value)
        except ValueError:
            # Thử convert sang bool
            if env_value.lower() in ('true', 'false'):
                return env_value.lower() == 'true'
            return env_value
    
    # Fallback về default config
    keys = key.split('.')
    value = DEFAULT_CONFIG
    for k in keys:
        if isinstance(value, dict) and k in value:
            value = value[k]
        else:
            return default
    
    return value

def validate_pagination_params(page: Optional[int] = None, limit: Optional[int] = None) -> Dict[str, int]:
    """
    Validate và chuẩn hóa pagination parameters
    
    Args:
        page: Số trang
        limit: Số lượng items per page
        
    Returns:
        Dict chứa page và limit đã được validate
    """
    default_page = get_config_value("pagination.default_page", 1)
    default_limit = get_config_value("pagination.default_limit", 10)
    max_limit = get_config_value("pagination.max_limit", 100)
    
    # Validate page
    if page is None or page < 1:
        page = default_page
    
    # Validate limit
    if limit is None or limit < 1:
        limit = default_limit
    elif limit > max_limit:
        limit = max_limit
    
    return {"page": page, "limit": limit}

def format_file_size(size_bytes: int) -> str:
    """
    Format file size thành human readable string
    
    Args:
        size_bytes: Kích thước file tính bằng bytes
        
    Returns:
        String đã format (ví dụ: "1.5 MB")
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    return f"{size:.1f} {size_names[i]}"

def get_media_status_description(status: str) -> str:
    """
    Lấy mô tả cho media status
    
    Args:
        status: Media status
        
    Returns:
        Mô tả status
    """
    descriptions = {
        MediaStatus.DRAFT.value: "Bản nháp - chưa được duyệt",
        MediaStatus.APPROVED.value: "Đã được phê duyệt",
        MediaStatus.PENDING.value: "Đang chờ phê duyệt", 
        MediaStatus.REJECTED.value: "Đã bị từ chối"
    }
    return descriptions.get(status, f"Trạng thái không xác định: {status}")

def build_search_params(
    page: Optional[int] = None,
    limit: Optional[int] = None,
    search: Optional[str] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    Xây dựng parameters cho API search requests
    
    Args:
        page: Số trang
        limit: Số lượng items per page
        search: Từ khóa tìm kiếm
        **kwargs: Các parameters khác
        
    Returns:
        Dict chứa parameters đã được chuẩn hóa
    """
    # Validate pagination
    pagination = validate_pagination_params(page, limit)
    params = pagination.copy()
    
    # Thêm search keyword nếu có
    if search and search.strip():
        params["search"] = search.strip()
    
    # Thêm các parameters khác
    for key, value in kwargs.items():
        if value is not None:
            params[key] = value
    
    return params

def get_error_message(error_code: int, default_message: str = "Đã xảy ra lỗi") -> str:
    """
    Lấy error message dựa trên error code
    
    Args:
        error_code: HTTP error code
        default_message: Message mặc định
        
    Returns:
        Error message
    """
    error_messages = {
        400: "Yêu cầu không hợp lệ - vui lòng kiểm tra lại dữ liệu đầu vào",
        401: "Không có quyền truy cập - vui lòng kiểm tra token xác thực",
        403: "Bị cấm truy cập - bạn không có quyền thực hiện hành động này",
        404: "Không tìm thấy tài nguyên - dữ liệu có thể đã bị xóa hoặc không tồn tại",
        429: "Quá nhiều yêu cầu - vui lòng thử lại sau",
        500: "Lỗi máy chủ nội bộ - vui lòng thử lại sau",
        502: "Lỗi gateway - máy chủ tạm thời không khả dụng",
        503: "Dịch vụ tạm thời không khả dụng - vui lòng thử lại sau"
    }
    
    return error_messages.get(error_code, default_message)

# Export các constants và functions chính
__all__ = [
    "MediaStatus",
    "OwnerType", 
    "SortDirection",
    "URLSortBy",
    "DEFAULT_CONFIG",
    "TOOL_NAME_MAPPING",
    "ENDPOINT_EMOJIS",
    "get_config_value",
    "validate_pagination_params",
    "format_file_size",
    "get_media_status_description",
    "build_search_params",
    "get_error_message"
]
