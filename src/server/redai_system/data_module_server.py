"""
Server MCP cho RedAI Data Module API sử dụng FastMCP và OpenAPI Schema

Server này tự động tạo các MCP tools và resources từ data/swagger.json,
cung cấp giao diện MCP cho tất cả các endpoint của Data Module API.

Tính năng:
- Tự động tạo tools từ OpenAPI specification
- Hỗ trợ Bearer token authentication
- HTTP transport với FastMCP
- Tùy chỉnh route mapping cho các loại endpoint khác nhau
- Xử lý parameters, headers và request body tự động

Cấu trúc API:
- Media endpoints: Quản lý media files của user
- URL endpoints: Quản lý URL resources của user  
- Statistics endpoints: Thống kê dữ liệu của user

Authentication:
- Bearer token qua header Authorization: Bearer <token>
- Tự động xử lý authentication cho tất cả requests
"""

import os
import json
import httpx
from pathlib import Path
from typing import Optional, Dict, Any

# FastMCP imports
from fastmcp import FastMCP

# Cấu hình môi trường
API_BASE_URL = os.getenv("REDAI_DATA_API_BASE_URL", "https://api.redai.com")
BEARER_TOKEN = os.getenv("REDAI_BEARER_TOKEN", "")
HTTP_HOST = os.getenv("DATA_MODULE_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("DATA_MODULE_HTTP_PORT", "8002"))
HTTP_PATH = os.getenv("DATA_MODULE_HTTP_PATH", "/mcp")

def load_openapi_schema() -> dict:
    """
    Tải OpenAPI schema từ file data/swagger.json
    """
    schema_path = Path(__file__).parent / "data" / "swagger.json"
    if not schema_path.exists():
        raise FileNotFoundError(f"Không tìm thấy file schema tại: {schema_path}")

    with open(schema_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def create_authenticated_client(base_url: str, bearer_token: Optional[str] = None) -> httpx.AsyncClient:
    """
    Tạo HTTP client với Bearer token authentication
    """
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    if bearer_token:
        headers["Authorization"] = f"Bearer {bearer_token}"
    
    return httpx.AsyncClient(
        base_url=base_url,
        headers=headers,
        timeout=30.0
    )

def customize_component_names() -> Dict[str, str]:
    """
    Tùy chỉnh tên cho các MCP components để dễ hiểu và sử dụng
    """
    return {
        # Media endpoints
        "get_media_my_media": "get_my_media_list",
        "delete_media_my_media": "delete_my_media_batch",
        
        # URL endpoints  
        "get_data_url": "get_my_urls",
        "post_data_url": "create_new_url",
        "get_data_url_by_id": "get_url_details",
        "put_data_url_by_id": "update_url",
        "delete_data_url_by_id": "delete_url",
        
        # Statistics endpoints
        "get_statistics": "get_user_statistics",
        "get_statistics_storage": "get_storage_statistics",
    }

def get_endpoint_description(path: str, method: str, operation: Dict[str, Any]) -> str:
    """
    Tạo mô tả chi tiết cho endpoint dựa trên OpenAPI spec
    """
    base_desc = operation.get("description", operation.get("summary", ""))
    
    # Thêm thông tin về parameters nếu có
    params = operation.get("parameters", [])
    if params:
        param_info = []
        for param in params:
            param_name = param.get("name", "")
            param_desc = param.get("description", "")
            param_required = param.get("required", False)
            required_mark = " (bắt buộc)" if param_required else " (tùy chọn)"
            param_info.append(f"- {param_name}: {param_desc}{required_mark}")
        
        if param_info:
            base_desc += f"\n\nTham số:\n" + "\n".join(param_info)
    
    # Thêm thông tin về request body nếu có
    request_body = operation.get("requestBody")
    if request_body:
        body_desc = request_body.get("description", "")
        if body_desc:
            base_desc += f"\n\nDữ liệu gửi: {body_desc}"
    
    return base_desc

def customize_mcp_components(mcp_server: FastMCP, openapi_spec: Dict[str, Any]) -> None:
    """
    Tùy chỉnh MCP components sau khi được tạo từ OpenAPI spec
    """
    paths = openapi_spec.get("paths", {})
    
    # Duyệt qua tất cả tools và resources để tùy chỉnh
    for tool_name, tool in mcp_server._tools.items():
        # Tìm endpoint tương ứng để lấy thông tin chi tiết
        for path, path_item in paths.items():
            for method, operation in path_item.items():
                operation_id = operation.get("operationId", "")
                if operation_id and operation_id.lower() in tool_name.lower():
                    # Cập nhật description với thông tin chi tiết
                    enhanced_desc = get_endpoint_description(path, method, operation)
                    tool.description = enhanced_desc
                    
                    # Thêm tags dựa trên OpenAPI tags
                    api_tags = operation.get("tags", [])
                    if api_tags:
                        # Thêm emoji dựa trên tag
                        tag_emojis = {
                            "User Media": "🖼️",
                            "User URL": "🔗", 
                            "User Statistics": "📊"
                        }
                        for tag in api_tags:
                            emoji = tag_emojis.get(tag, "🔧")
                            if not tool.description.startswith(emoji):
                                tool.description = f"{emoji} {tool.description}"
                    break

def create_data_module_server() -> FastMCP:
    """
    Tạo MCP server từ OpenAPI schema cho Data Module API
    """
    # Tải OpenAPI schema
    openapi_spec = load_openapi_schema()
    
    # Tạo authenticated HTTP client
    api_client = create_authenticated_client(API_BASE_URL, BEARER_TOKEN)
    
    # Tạo MCP server từ OpenAPI spec
    try:
        mcp = FastMCP.from_openapi(
            openapi_spec=openapi_spec,
            client=api_client,
            name="RedAI-Data-Module-Server",
            timeout=30.0,
        )
    except TypeError:
        # Fallback cho phiên bản cũ hơn của FastMCP
        mcp = FastMCP.from_openapi(
            openapi_spec=openapi_spec,
            client=api_client,
        )
    
    # Tùy chỉnh components
    customize_mcp_components(mcp, openapi_spec)
    
    return mcp

# Tạo MCP server instance
mcp = create_data_module_server()

def print_server_info():
    """
    In thông tin về server và các endpoints có sẵn
    """
    try:
        schema = load_openapi_schema()
        endpoints = list(schema.get("paths", {}).keys())
        
        print("="*70)
        print("🚀 RedAI Data Module MCP Server")
        print("="*70)
        print("📋 Cấu hình:")
        print(f"   🌐 API Base URL: {API_BASE_URL}")
        print(f"   🔑 Bearer Token: {'✅ Đã cấu hình' if BEARER_TOKEN else '❌ Chưa cấu hình'}")
        print(f"   🚀 Transport: Streamable HTTP")
        print(f"   🌐 Server URL: http://{HTTP_HOST}:{HTTP_PORT}")
        print(f"   📡 MCP Endpoint: http://{HTTP_HOST}:{HTTP_PORT}{HTTP_PATH}")
        print()
        
        print(f"📊 API Information:")
        print(f"   📖 Title: {schema.get('info', {}).get('title', 'N/A')}")
        print(f"   📝 Description: {schema.get('info', {}).get('description', 'N/A')}")
        print(f"   🔢 Version: {schema.get('info', {}).get('version', 'N/A')}")
        print(f"   📍 Endpoints: {len(endpoints)} endpoints")
        print()
        
        print("📋 Available Endpoints:")
        for path in endpoints:
            methods = list(schema["paths"][path].keys())
            # Lấy tags từ operation đầu tiên
            first_method = list(schema["paths"][path].values())[0]
            tags = first_method.get("tags", [])
            tag_str = f" [{', '.join(tags)}]" if tags else ""
            print(f"   • {path} [{', '.join(method.upper() for method in methods)}]{tag_str}")
        
        if not BEARER_TOKEN:
            print()
            print("⚠️  CẢNH BÁO: Bearer Token chưa được cấu hình!")
            print("📋 Để sử dụng API thực tế:")
            print("   1. Thiết lập biến môi trường REDAI_BEARER_TOKEN")
            print("   2. Hoặc cập nhật BEARER_TOKEN trong code")
            print("   3. Khởi động lại server")
            print("🧪 Server vẫn có thể chạy để test cấu trúc MCP")
        
        print("="*70)
        
    except Exception as e:
        print(f"❌ Lỗi khi tải thông tin server: {str(e)}")

def main():
    """
    Hàm main để khởi chạy MCP server
    """
    try:
        # Thiết lập encoding cho Windows console
        import sys
        if sys.platform == "win32":
            import os
            os.system("chcp 65001 > nul")  # Set UTF-8 encoding
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        
        # In thông tin server
        print_server_info()
        print("🚀 Đang khởi động server...")
        
        # Chạy MCP server với Streamable HTTP transport
        mcp.run(
            transport="streamable-http",
            host=HTTP_HOST,
            port=HTTP_PORT,
            path=HTTP_PATH
        )
        
    except KeyboardInterrupt:
        print("\n⏹️  Server đã được dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server: {str(e)}")
        import traceback
        traceback.print_exc()
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
