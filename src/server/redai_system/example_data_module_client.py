"""
Ví dụ sử dụng RedAI Data Module MCP Server với authentication từ client

Script này demo cách kết nối và sử dụng Data Module MCP Server
với Bearer token được truyền từ client.
"""

import asyncio
import os
import sys
from pathlib import Path

# Thêm path để import
sys.path.append(str(Path(__file__).parent))

from fastmcp import Client

# Cấu hình
SERVER_URL = "http://127.0.0.1:8002/mcp"
BEARER_TOKEN = os.getenv("REDAI_BEARER_TOKEN", "your_bearer_token_here")

async def demo_authentication():
    """Demo cách cấu hình authentication"""
    print("🔐 Demo Authentication")
    print("=" * 50)
    
    async with Client(SERVER_URL) as client:
        # Bước 1: Kiểm tra trạng thái ban đầu
        print("1️⃣ Kiểm tra trạng thái authentication ban đầu...")
        status = await client.call_tool("check_auth_status")
        print(f"   Status: {status.text}")
        print()
        
        # Bước 2: Cấu hình Bearer token
        print("2️⃣ Cấu hình Bearer token...")
        auth_result = await client.call_tool("update_bearer_token", {
            "bearer_token": BEARER_TOKEN
        })
        print(f"   Result: {auth_result.text}")
        print()
        
        # Bước 3: Kiểm tra lại trạng thái
        print("3️⃣ Kiểm tra trạng thái sau khi cấu hình...")
        status = await client.call_tool("check_auth_status")
        print(f"   Status: {status.text}")
        print()

async def demo_media_management():
    """Demo quản lý media"""
    print("🖼️ Demo Media Management")
    print("=" * 50)
    
    async with Client(SERVER_URL) as client:
        # Cấu hình auth
        await client.call_tool("update_bearer_token", {
            "bearer_token": BEARER_TOKEN
        })
        
        # Lấy danh sách media
        print("1️⃣ Lấy danh sách media...")
        try:
            media_list = await client.call_tool("get_my_media_list", {
                "page": 1,
                "limit": 5,
                "search": "image"
            })
            print(f"   Result: {media_list.text[:200]}...")
        except Exception as e:
            print(f"   Error: {str(e)}")
        print()
        
        # Demo xóa media (chỉ demo, không thực thi)
        print("2️⃣ Demo xóa media (không thực thi)...")
        print("   Tool: delete_my_media_batch")
        print("   Parameters: {'mediaIds': ['uuid1', 'uuid2']}")
        print("   Note: Cần có media IDs thực tế để test")
        print()

async def demo_url_management():
    """Demo quản lý URLs"""
    print("🔗 Demo URL Management")
    print("=" * 50)
    
    async with Client(SERVER_URL) as client:
        # Cấu hình auth
        await client.call_tool("update_bearer_token", {
            "bearer_token": BEARER_TOKEN
        })
        
        # Lấy danh sách URLs
        print("1️⃣ Lấy danh sách URLs...")
        try:
            urls = await client.call_tool("get_my_urls", {
                "page": 1,
                "limit": 5,
                "keyword": "tutorial",
                "sortBy": "createdAt",
                "sortDirection": "DESC"
            })
            print(f"   Result: {urls.text[:200]}...")
        except Exception as e:
            print(f"   Error: {str(e)}")
        print()
        
        # Tạo URL mới
        print("2️⃣ Tạo URL mới...")
        try:
            new_url = await client.call_tool("create_new_url", {
                "url": "https://example.com/demo-article",
                "title": "Demo Article từ MCP Client",
                "content": "Đây là bài viết demo được tạo từ MCP client để test API",
                "type": "web",
                "tags": ["demo", "mcp", "test"],
                "isActive": True
            })
            print(f"   Result: {new_url.text[:200]}...")
        except Exception as e:
            print(f"   Error: {str(e)}")
        print()

async def demo_statistics():
    """Demo thống kê"""
    print("📊 Demo Statistics")
    print("=" * 50)
    
    async with Client(SERVER_URL) as client:
        # Cấu hình auth
        await client.call_tool("update_bearer_token", {
            "bearer_token": BEARER_TOKEN
        })
        
        # Lấy thống kê user
        print("1️⃣ Lấy thống kê user...")
        try:
            stats = await client.call_tool("get_user_statistics")
            print(f"   Result: {stats.text}")
        except Exception as e:
            print(f"   Error: {str(e)}")
        print()
        
        # Lấy thống kê storage
        print("2️⃣ Lấy thống kê storage...")
        try:
            storage_stats = await client.call_tool("get_storage_statistics")
            print(f"   Result: {storage_stats.text}")
        except Exception as e:
            print(f"   Error: {str(e)}")
        print()

async def demo_list_all_tools():
    """Demo liệt kê tất cả tools"""
    print("🔧 Demo List All Tools")
    print("=" * 50)
    
    async with Client(SERVER_URL) as client:
        # Lấy danh sách tools
        tools = await client.list_tools()
        
        print(f"Tổng số tools: {len(tools)}")
        print()
        
        # Phân loại tools
        auth_tools = []
        media_tools = []
        url_tools = []
        stats_tools = []
        other_tools = []
        
        for tool in tools:
            name = tool.name.lower()
            if "auth" in name or "token" in name:
                auth_tools.append(tool)
            elif "media" in name:
                media_tools.append(tool)
            elif "url" in name:
                url_tools.append(tool)
            elif "statistic" in name:
                stats_tools.append(tool)
            else:
                other_tools.append(tool)
        
        # In ra từng loại
        categories = [
            ("🔐 Authentication Tools", auth_tools),
            ("🖼️ Media Tools", media_tools),
            ("🔗 URL Tools", url_tools),
            ("📊 Statistics Tools", stats_tools),
            ("🔧 Other Tools", other_tools)
        ]
        
        for category_name, category_tools in categories:
            if category_tools:
                print(f"{category_name}:")
                for tool in category_tools:
                    print(f"   • {tool.name}: {tool.description[:80]}...")
                print()

async def run_all_demos():
    """Chạy tất cả demos"""
    print("🚀 RedAI Data Module MCP Client Demo")
    print("=" * 70)
    print(f"Server URL: {SERVER_URL}")
    print(f"Bearer Token: {'✅ Configured' if BEARER_TOKEN != 'your_bearer_token_here' else '❌ Not configured'}")
    print("=" * 70)
    print()
    
    try:
        # Demo 1: List tools
        await demo_list_all_tools()
        
        # Demo 2: Authentication
        await demo_authentication()
        
        # Demo 3: Media management
        await demo_media_management()
        
        # Demo 4: URL management
        await demo_url_management()
        
        # Demo 5: Statistics
        await demo_statistics()
        
        print("✅ Tất cả demos đã hoàn thành!")
        
    except Exception as e:
        print(f"❌ Lỗi trong quá trình demo: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """Main function"""
    try:
        # Thiết lập encoding cho Windows
        if sys.platform == "win32":
            import os
            os.system("chcp 65001 > nul")
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        
        print("💡 Lưu ý:")
        print("   1. Đảm bảo Data Module MCP Server đang chạy tại", SERVER_URL)
        print("   2. Cấu hình REDAI_BEARER_TOKEN environment variable hoặc sửa BEARER_TOKEN trong code")
        print("   3. Một số API calls có thể fail nếu không có dữ liệu thực tế")
        print()
        
        # Chạy demos
        asyncio.run(run_all_demos())
        
    except KeyboardInterrupt:
        print("\n⏹️ Demo đã được dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi khi chạy demo: {str(e)}")

if __name__ == "__main__":
    main()
