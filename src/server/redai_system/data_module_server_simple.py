"""
Server MCP đơn giản cho RedAI Data Module API - chỉ sử dụng STDIO transport

Phiên bản này tr<PERSON>h các vấn đề với HTTP transport và tập trung vào
việc tạo tools từ OpenAPI specification.
"""

import os
import json
import httpx
from pathlib import Path
from typing import Optional, Dict, Any

# FastMCP imports
from fastmcp import FastMCP, Context

# Cấu hình môi trường
API_BASE_URL = os.getenv("REDAI_DATA_API_BASE_URL", "https://api.redai.com")

def load_openapi_schema() -> dict:
    """
    Tải OpenAPI schema từ file swagger.json trong cùng thư mục
    """
    schema_path = Path(__file__).parent / "data" / "swagger.json"
    if not schema_path.exists():
        raise FileNotFoundError(f"Không tìm thấy file schema tại: {schema_path}")

    with open(schema_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def create_authenticated_client(base_url: str, bearer_token: Optional[str] = None) -> httpx.AsyncClient:
    """
    Tạo HTTP client với Bearer token authentication
    """
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    if bearer_token:
        headers["Authorization"] = f"Bearer {bearer_token}"
    
    return httpx.AsyncClient(
        base_url=base_url,
        headers=headers,
        timeout=30.0
    )

class SimpleDataModuleServer:
    """
    Class đơn giản để quản lý Data Module MCP Server
    """
    
    def __init__(self):
        self.openapi_spec = load_openapi_schema()
        self.base_url = API_BASE_URL
        self.current_token = None
        
    def create_server(self, bearer_token: Optional[str] = None) -> FastMCP:
        """
        Tạo MCP server đơn giản
        """
        # Tạo authenticated HTTP client
        api_client = create_authenticated_client(self.base_url, bearer_token)
        
        # Tạo MCP server từ OpenAPI spec
        try:
            mcp = FastMCP.from_openapi(
                openapi_spec=self.openapi_spec,
                client=api_client,
                name="RedAI-Data-Module-Server-Simple",
            )
        except TypeError:
            # Fallback cho phiên bản cũ hơn của FastMCP
            mcp = FastMCP.from_openapi(
                openapi_spec=self.openapi_spec,
                client=api_client,
            )
        
        # Thêm authentication tools
        self._add_auth_tools(mcp)
        
        return mcp
    
    def _add_auth_tools(self, mcp_server: FastMCP) -> None:
        """
        Thêm các tools để quản lý authentication
        """
        
        @mcp_server.tool()
        async def update_bearer_token(bearer_token: str) -> str:
            """
            Cập nhật Bearer token cho các API calls tiếp theo
            
            Args:
                bearer_token: Bearer token mới để xác thực
                
            Returns:
                Thông báo xác nhận việc cập nhật token
            """
            try:
                self.current_token = bearer_token
                return f"✅ Bearer token đã được cập nhật thành công."
                
            except Exception as e:
                return f"❌ Không thể cập nhật Bearer token: {str(e)}"
        
        @mcp_server.tool()
        async def check_auth_status() -> str:
            """
            Kiểm tra trạng thái authentication hiện tại
            
            Returns:
                Thông tin về trạng thái authentication
            """
            try:
                if self.current_token:
                    return "✅ Authentication đã được cấu hình và sẵn sàng sử dụng"
                else:
                    return "⚠️ Chưa có Bearer token. Sử dụng tool 'update_bearer_token' để cấu hình authentication"
                    
            except Exception as e:
                return f"❌ Không thể kiểm tra trạng thái authentication: {str(e)}"
        
        @mcp_server.tool()
        async def get_server_info() -> str:
            """
            Lấy thông tin về server và API endpoints
            
            Returns:
                Thông tin chi tiết về server
            """
            try:
                info = self.openapi_spec.get('info', {})
                paths = self.openapi_spec.get('paths', {})
                
                result = f"""
🚀 RedAI Data Module MCP Server
📖 Title: {info.get('title', 'N/A')}
📝 Description: {info.get('description', 'N/A')}
🔢 Version: {info.get('version', 'N/A')}
🌐 API Base URL: {self.base_url}
📍 Endpoints: {len(paths)} endpoints

📋 Available Endpoints:
"""
                for path, methods in paths.items():
                    method_list = list(methods.keys())
                    first_method = list(methods.values())[0]
                    tags = first_method.get("tags", [])
                    tag_str = f" [{', '.join(tags)}]" if tags else ""
                    result += f"   • {path} [{', '.join(method.upper() for method in method_list)}]{tag_str}\n"
                
                return result.strip()
                
            except Exception as e:
                return f"❌ Không thể lấy thông tin server: {str(e)}"

# Tạo instance của SimpleDataModuleServer
simple_server = SimpleDataModuleServer()

# Tạo MCP server mặc định
mcp = simple_server.create_server()

def print_server_info():
    """
    In thông tin về server
    """
    try:
        schema = simple_server.openapi_spec
        endpoints = list(schema.get("paths", {}).keys())
        
        print("="*70)
        print("🚀 RedAI Data Module MCP Server (Simple)")
        print("="*70)
        print("📋 Cấu hình:")
        print(f"   🌐 API Base URL: {API_BASE_URL}")
        print(f"   🔑 Authentication: Nhận từ client")
        print(f"   🚀 Transport: STDIO")
        print()
        
        print(f"📊 API Information:")
        print(f"   📖 Title: {schema.get('info', {}).get('title', 'N/A')}")
        print(f"   📝 Description: {schema.get('info', {}).get('description', 'N/A')}")
        print(f"   🔢 Version: {schema.get('info', {}).get('version', 'N/A')}")
        print(f"   📍 Endpoints: {len(endpoints)} endpoints")
        print()
        
        print("📋 Available Endpoints:")
        for path in endpoints:
            methods = list(schema["paths"][path].keys())
            first_method = list(schema["paths"][path].values())[0]
            tags = first_method.get("tags", [])
            tag_str = f" [{', '.join(tags)}]" if tags else ""
            print(f"   • {path} [{', '.join(method.upper() for method in methods)}]{tag_str}")
        
        print()
        print("🔧 Built-in Tools:")
        print("   • update_bearer_token: Cập nhật Bearer token")
        print("   • check_auth_status: Kiểm tra trạng thái authentication")
        print("   • get_server_info: Lấy thông tin server")
        
        print("="*70)
        
    except Exception as e:
        print(f"❌ Lỗi khi tải thông tin server: {str(e)}")

def main():
    """
    Hàm main để khởi chạy MCP server với STDIO
    """
    try:
        # Thiết lập encoding cho Windows console
        import sys
        if sys.platform == "win32":
            import os
            os.system("chcp 65001 > nul")  # Set UTF-8 encoding
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        
        # In thông tin server
        print_server_info()
        print("🚀 Đang khởi động server với STDIO transport...")
        print("💡 Sử dụng tool 'update_bearer_token' để cấu hình authentication")
        print("💡 Sử dụng tool 'get_server_info' để xem thông tin chi tiết")
        print()
        
        # Chạy MCP server với STDIO transport
        mcp.run(transport="stdio")
        
    except KeyboardInterrupt:
        print("\n⏹️  Server đã được dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
