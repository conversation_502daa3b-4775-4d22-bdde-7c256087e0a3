"""
RedAI System MCP Servers Package

Package này chứa các MCP servers cho hệ thống RedAI:
- shipment_server: Server cho các API vận chuyển (GHN, GHTK, J&T, Ahamove)
- user_api_server: Server cho User API endpoints
- data_module_server: Server cho Data Module API endpoints

Mỗi server được xây dựng bằng FastMCP framework và hỗ trợ:
- HTTP transport
- Authentication (JWT/Bearer token)
- Tự động tạo tools từ OpenAPI specs
- Error handling và logging
"""

# Import các servers chính
from .shipment_server import mcp as shipment_mcp
from .user_api_server import mcp as user_api_mcp
from .data_module_server import mcp as data_module_mcp

# Import các utility modules
from . import utils
from . import tools
from . import resources

# Export các components chính
__all__ = [
    "shipment_mcp",
    "user_api_mcp",
    "data_module_mcp",
    "utils",
    "tools",
    "resources"
]

# Metadata
__version__ = "1.0.0"
__author__ = "RedAI Development Team"
__description__ = "MCP Servers cho hệ thống RedAI"