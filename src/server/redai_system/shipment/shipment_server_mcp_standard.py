#!/usr/bin/env python3
"""
Shipment MCP Server - Chuẩn MCP SDK chính thức

Server n<PERSON><PERSON> tuân thủ đúng chuẩn MCP Python SDK với Streamable HTTP transport.
Sử dụng low-level MCP server implementation theo tài liệu chính thức.

Tính năng:
- Streamable HTTP transport (chuẩn MCP)
- Session management với session ID
- Proper error handling và security
- Support cho tất cả MCP protocol messages
- GHN + GHTK + J&T + Ahamove integration

Endpoints:
- POST /mcp - MCP endpoint chính
- GET /mcp - Optional SSE stream cho server-initiated messages
"""

import os
import json
import asyncio
import uvicorn
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv
from starlette.applications import Starlette
from starlette.routing import Route
from starlette.requests import Request
from starlette.responses import Response, JSONResponse, StreamingResponse
from starlette.middleware.cors import CORSMiddleware

# MCP SDK imports - Low-level implementation
import mcp.types as types
from mcp.server.lowlevel import Server

# Import cấu hình và utils
try:
    # Thử relative imports trước
    from .utils import (
        ShipmentEnvironment,
        GHN_TOKEN, GHN_SHOP_ID, GHN_ENVIRONMENT, GHN_BASE_URL,
        GHTK_TOKEN, GHTK_PARTNER_CODE, GHTK_ENVIRONMENT, GHTK_BASE_URL,
        JT_USERNAME, JT_API_KEY, JT_CUSTOMER_CODE, JT_ENVIRONMENT, JT_BASE_URL,
        AHAMOVE_API_KEY, AHAMOVE_TOKEN, AHAMOVE_MOBILE, AHAMOVE_ENVIRONMENT, AHAMOVE_BASE_URL,
        BASE_URL, make_ghn_request
    )
    
    # Import tools registration functions
    from src.server.redai_system.shipment.tools.ghn_tools import register_ghn_tools
    from src.server.redai_system.shipment.tools import register_ghtk_tools
    from src.server.redai_system.shipment.tools.jt_tools import register_jt_tools
    from src.server.redai_system.shipment.tools.ahamove_tools import register_ahamove_tools
    
    # Import resources registration functions
    from .resources.ghn_resources import register_ghn_resources
    from .resources.ghtk_resources import register_ghtk_resources
    from .resources.jt_resources import register_jt_resources
    from .resources.ahamove_resources import register_ahamove_resources

except ImportError:
    # Fallback cho absolute imports
    import sys
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    from utils import (
        ShipmentEnvironment,
        GHN_TOKEN, GHN_SHOP_ID, GHN_ENVIRONMENT, GHN_BASE_URL,
        GHTK_TOKEN, GHTK_PARTNER_CODE, GHTK_ENVIRONMENT, GHTK_BASE_URL,
        JT_USERNAME, JT_API_KEY, JT_CUSTOMER_CODE, JT_ENVIRONMENT, JT_BASE_URL,
        AHAMOVE_API_KEY, AHAMOVE_TOKEN, AHAMOVE_MOBILE, AHAMOVE_ENVIRONMENT, AHAMOVE_BASE_URL,
        BASE_URL, make_ghn_request
    )
    
    from src.server.redai_system.shipment.tools import register_ghn_tools
    from src.server.redai_system.shipment.tools.ghtk_tools import register_ghtk_tools
    from src.server.redai_system.shipment.tools import register_jt_tools
    from src.server.redai_system.shipment.tools import register_ahamove_tools
    
    from resources.ghn_resources import register_ghn_resources
    from resources.ghtk_resources import register_ghtk_resources
    from resources.jt_resources import register_jt_resources
    from resources.ahamove_resources import register_ahamove_resources

# Load environment variables
load_dotenv()

# Cấu hình server
HTTP_HOST = os.getenv("GHN_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("GHN_HTTP_PORT", "8000"))
HTTP_PATH = os.getenv("GHN_HTTP_PATH", "/mcp")

# ============================================================================
# MCP SERVER SETUP - Low-level implementation
# ============================================================================

# Khởi tạo MCP server với low-level API
mcp_server = Server("Shipment-Server-GHN-GHTK-JT-Ahamove")

# Session management
active_sessions: Dict[str, Dict[str, Any]] = {}

def generate_session_id() -> str:
    """Tạo session ID an toàn"""
    import secrets
    return secrets.token_urlsafe(32)

# ============================================================================
# TOOLS REGISTRATION - Chuyển đổi từ FastMCP sang Low-level
# ============================================================================

class ToolsRegistry:
    """Registry để chuyển đổi tools từ FastMCP sang low-level MCP"""
    
    def __init__(self):
        self.tools: Dict[str, Any] = {}
        self.resources: Dict[str, Any] = {}
    
    def register_tool(self, name: str, description: str, input_schema: Dict, handler):
        """Đăng ký tool"""
        self.tools[name] = {
            "name": name,
            "description": description,
            "inputSchema": input_schema,
            "handler": handler
        }
    
    def register_resource(self, uri_template: str, name: str, description: str, handler):
        """Đăng ký resource"""
        self.resources[uri_template] = {
            "uri": uri_template,
            "name": name,
            "description": description,
            "handler": handler
        }

# Khởi tạo registry
tools_registry = ToolsRegistry()

# ============================================================================
# MCP REQUEST HANDLERS
# ============================================================================

@mcp_server.list_tools()
async def handle_list_tools() -> List[types.Tool]:
    """Trả về danh sách tools có sẵn"""
    tools = []
    for tool_info in tools_registry.tools.values():
        tools.append(types.Tool(
            name=tool_info["name"],
            description=tool_info["description"],
            inputSchema=tool_info["inputSchema"]
        ))
    return tools

@mcp_server.call_tool()
async def handle_call_tool(name: str, arguments: Optional[Dict[str, Any]]) -> List[types.TextContent]:
    """Xử lý tool calls"""
    if name not in tools_registry.tools:
        raise ValueError(f"Tool '{name}' not found")
    
    tool_info = tools_registry.tools[name]
    handler = tool_info["handler"]
    
    try:
        # Gọi handler với arguments
        result = await handler(arguments or {})
        
        # Chuyển đổi result thành TextContent
        if isinstance(result, str):
            content = result
        elif isinstance(result, dict):
            content = json.dumps(result, ensure_ascii=False, indent=2)
        else:
            content = str(result)
        
        return [types.TextContent(type="text", text=content)]
    
    except Exception as e:
        error_msg = f"Error executing tool '{name}': {str(e)}"
        return [types.TextContent(type="text", text=error_msg)]

@mcp_server.list_resources()
async def handle_list_resources() -> List[types.Resource]:
    """Trả về danh sách resources có sẵn"""
    resources = []
    for resource_info in tools_registry.resources.values():
        resources.append(types.Resource(
            uri=resource_info["uri"],
            name=resource_info["name"],
            description=resource_info["description"],
            mimeType="application/json"
        ))
    return resources

@mcp_server.read_resource()
async def handle_read_resource(uri: str) -> str:
    """Đọc resource theo URI"""
    # Tìm resource handler phù hợp
    for uri_template, resource_info in tools_registry.resources.items():
        if uri.startswith(uri_template.split("{")[0]):  # Simple matching
            handler = resource_info["handler"]
            try:
                result = await handler(uri)
                if isinstance(result, dict):
                    return json.dumps(result, ensure_ascii=False, indent=2)
                return str(result)
            except Exception as e:
                raise ValueError(f"Error reading resource '{uri}': {str(e)}")
    
    raise ValueError(f"Resource '{uri}' not found")

# ============================================================================
# TOOLS & RESOURCES REGISTRATION
# ============================================================================

def register_all_tools_and_resources():
    """Đăng ký tất cả tools và resources"""
    
    # Tạo mock FastMCP object để sử dụng với existing registration functions
    class MockFastMCP:
        def __init__(self, registry):
            self.registry = registry
        
        def tool(self, name: str = None, description: str = None):
            def decorator(func):
                tool_name = name or func.__name__
                tool_desc = description or func.__doc__ or f"Tool: {tool_name}"
                
                # Extract input schema from function signature
                import inspect
                sig = inspect.signature(func)
                input_schema = {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
                
                for param_name, param in sig.parameters.items():
                    input_schema["properties"][param_name] = {"type": "string"}
                    if param.default == inspect.Parameter.empty:
                        input_schema["required"].append(param_name)
                
                self.registry.register_tool(tool_name, tool_desc, input_schema, func)
                return func
            return decorator
        
        def resource(self, uri_template: str):
            def decorator(func):
                resource_name = func.__name__
                resource_desc = func.__doc__ or f"Resource: {resource_name}"
                self.registry.register_resource(uri_template, resource_name, resource_desc, func)
                return func
            return decorator
    
    # Tạo mock object
    mock_mcp = MockFastMCP(tools_registry)
    
    # Đăng ký tất cả tools và resources
    try:
        register_ghn_tools(mock_mcp)
        register_ghtk_tools(mock_mcp)
        register_jt_tools(mock_mcp)
        register_ahamove_tools(mock_mcp)
        
        register_ghn_resources(mock_mcp)
        register_ghtk_resources(mock_mcp)
        register_jt_resources(mock_mcp)
        register_ahamove_resources(mock_mcp)
        
        print(f"✅ Đã đăng ký {len(tools_registry.tools)} tools và {len(tools_registry.resources)} resources")
    
    except Exception as e:
        print(f"❌ Lỗi khi đăng ký tools/resources: {e}")
        import traceback
        traceback.print_exc()

# ============================================================================
# STREAMABLE HTTP TRANSPORT IMPLEMENTATION
# ============================================================================

async def handle_mcp_post(request: Request) -> Response:
    """Xử lý POST requests đến MCP endpoint"""
    try:
        # Validate Content-Type
        content_type = request.headers.get("content-type", "")
        if not content_type.startswith("application/json"):
            return JSONResponse(
                {"error": "Content-Type must be application/json"},
                status_code=400
            )
        
        # Validate Accept header
        accept = request.headers.get("accept", "")
        if "text/event-stream" not in accept and "application/json" not in accept:
            return JSONResponse({
                "jsonrpc": "2.0",
                "id": "server-error",
                "error": {
                    "code": -32600,
                    "message": "Not Acceptable: Client must accept text/event-stream"
                }
            }, status_code=406)
        
        # Parse JSON-RPC request
        body = await request.body()
        try:
            json_rpc_request = json.loads(body)
        except json.JSONDecodeError:
            return JSONResponse({
                "jsonrpc": "2.0",
                "id": None,
                "error": {
                    "code": -32700,
                    "message": "Parse error"
                }
            }, status_code=400)
        
        # Handle session management
        session_id = request.headers.get("mcp-session-id")
        if not session_id and json_rpc_request.get("method") == "initialize":
            session_id = generate_session_id()
            active_sessions[session_id] = {"created_at": asyncio.get_event_loop().time()}
        
        # Process MCP request
        response_data = await process_mcp_request(json_rpc_request, session_id)
        
        # Return SSE response (chuẩn Streamable HTTP)
        async def generate_sse():
            yield f"event: message\n"
            yield f"data: {json.dumps(response_data, ensure_ascii=False)}\n\n"
        
        headers = {
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-cache, no-transform",
            "X-Accel-Buffering": "no"
        }
        
        if session_id:
            headers["Mcp-Session-Id"] = session_id
        
        return StreamingResponse(generate_sse(), headers=headers)
    
    except Exception as e:
        return JSONResponse({
            "jsonrpc": "2.0",
            "id": "server-error",
            "error": {
                "code": -32603,
                "message": f"Internal error: {str(e)}"
            }
        }, status_code=500)

async def process_mcp_request(json_rpc_request: Dict, session_id: Optional[str]) -> Dict:
    """Xử lý MCP request và trả về response"""
    method = json_rpc_request.get("method")
    params = json_rpc_request.get("params", {})
    request_id = json_rpc_request.get("id")
    
    try:
        if method == "initialize":
            # Initialize response
            result = {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {"listChanged": False},
                    "resources": {"subscribe": False, "listChanged": False},
                    "prompts": {"listChanged": False},
                    "experimental": {}
                },
                "serverInfo": {
                    "name": "Shipment-Server-GHN-GHTK-JT-Ahamove",
                    "version": "2.0.0"
                }
            }
            
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": result
            }
        
        elif method == "tools/list":
            tools = await handle_list_tools()
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {"tools": [tool.model_dump() for tool in tools]}
            }
        
        elif method == "tools/call":
            name = params.get("name")
            arguments = params.get("arguments", {})
            content = await handle_call_tool(name, arguments)
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {"content": [c.model_dump() for c in content]}
            }
        
        elif method == "resources/list":
            resources = await handle_list_resources()
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {"resources": [r.model_dump() for r in resources]}
            }
        
        elif method == "resources/read":
            uri = params.get("uri")
            content = await handle_read_resource(uri)
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "contents": [{
                        "uri": uri,
                        "mimeType": "application/json",
                        "text": content
                    }]
                }
            }
        
        else:
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {
                    "code": -32601,
                    "message": f"Method not found: {method}"
                }
            }
    
    except Exception as e:
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "error": {
                "code": -32603,
                "message": f"Internal error: {str(e)}"
            }
        }

async def handle_mcp_get(request: Request) -> Response:
    """Xử lý GET requests - Optional SSE stream cho server-initiated messages"""
    # Optional: Implement server-initiated SSE stream
    async def generate_sse():
        yield f"event: ping\n"
        yield f"data: {json.dumps({'type': 'ping', 'timestamp': asyncio.get_event_loop().time()})}\n\n"
    
    return StreamingResponse(generate_sse(), media_type="text/event-stream")

# ============================================================================
# STARLETTE APPLICATION SETUP
# ============================================================================

# Tạo Starlette application
app = Starlette(
    routes=[
        Route(HTTP_PATH, handle_mcp_post, methods=["POST"]),
        Route(HTTP_PATH, handle_mcp_get, methods=["GET"]),
    ]
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Trong production nên giới hạn origins
    allow_credentials=True,
    allow_methods=["GET", "POST", "DELETE"],
    allow_headers=["*"],
)

# ============================================================================
# MAIN FUNCTION
# ============================================================================

def main():
    """Khởi chạy MCP server với Streamable HTTP transport"""
    try:
        # Thiết lập encoding cho Windows console
        import sys
        if sys.platform == "win32":
            import os
            os.system("chcp 65001 > nul")
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        
        print("=" * 70)
        print("🚚 Khởi động Shipment MCP Server (Chuẩn MCP SDK)")
        print("=" * 70)
        print("📋 Transport: Streamable HTTP (Official MCP Standard)")
        print(f"🌐 Server URL: http://{HTTP_HOST}:{HTTP_PORT}")
        print(f"📡 MCP Endpoint: http://{HTTP_HOST}:{HTTP_PORT}{HTTP_PATH}")
        print(f"🔧 Protocol Version: 2024-11-05")
        print(f"📦 Session Management: Enabled")
        print()
        
        # Đăng ký tools và resources
        print("🔧 Đang đăng ký tools và resources...")
        register_all_tools_and_resources()
        
        print("=" * 70)
        print("🚀 Đang khởi động server...")
        
        # Chạy server với Uvicorn
        uvicorn.run(
            app,
            host=HTTP_HOST,
            port=HTTP_PORT,
            log_level="info",
            reload=False
        )
    
    except KeyboardInterrupt:
        print("\n⏹️  Server đã được dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server: {str(e)}")
        import traceback
        traceback.print_exc()
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
