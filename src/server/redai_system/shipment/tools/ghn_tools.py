"""
GHN Tools cho Shipment MCP Server

Module n<PERSON><PERSON> gộp tất cả các tools liên quan đến GHN API
Bao gồm: quản lý đơn hàng, t<PERSON>h to<PERSON>h<PERSON>, hỗ tr<PERSON> kh<PERSON>ch hàng, qu<PERSON><PERSON> lý cửa hàng
"""

import json
from typing import List, Optional
from mcp.server.fastmcp import FastMCP

# Import với fallback
try:
    from src.server.redai_system.utils import make_ghn_request, GHN_SHOP_ID
except ImportError:
    from utils import make_ghn_request, GHN_SHOP_ID

def register_ghn_tools(mcp: FastMCP):
    """Đăng ký tất cả GHN tools với MCP server"""

    # ORDER TOOLS - Quản lý đơn hàng
    @mcp.tool()
    async def create_order(
        to_name: str,
        to_phone: str,
        to_address: str,
        to_ward_code: str,
        to_district_id: int,
        cod_amount: int,
        content: str,
        weight: int,
        length: int,
        width: int,
        height: int,
        service_type_id: int,
        payment_type_id: int = 2,
        required_note: str = "KHONGCHOXEMHANG",
        client_order_code: Optional[str] = None,
        insurance_value: Optional[int] = None,
        coupon: Optional[str] = None,
        pick_shift: Optional[List[int]] = None
    ) -> str:
        """
        Tạo đơn hàng mới trên hệ thống GHN

        Args:
            to_name: Tên người nhận
            to_phone: Số điện thoại người nhận
            to_address: Địa chỉ người nhận
            to_ward_code: Mã phường/xã người nhận
            to_district_id: ID quận/huyện người nhận
            cod_amount: Số tiền thu hộ (COD)
            content: Nội dung hàng hóa
            weight: Khối lượng (gram)
            length: Chiều dài (cm)
            width: Chiều rộng (cm)
            height: Chiều cao (cm)
            service_type_id: ID loại dịch vụ
            payment_type_id: Hình thức thanh toán (1=Shop/Người gửi trả, 2=Người nhận trả)
            required_note: Ghi chú bắt buộc
            client_order_code: Mã đơn hàng của khách (tùy chọn)
            insurance_value: Giá trị bảo hiểm (tùy chọn)
            coupon: Mã giảm giá (tùy chọn)
            pick_shift: Danh sách ID ca lấy hàng (tùy chọn)

        Returns:
            JSON string chứa thông tin đơn hàng đã tạo
        """
        data = {
            "shop_id": int(GHN_SHOP_ID),
            "to_name": to_name,
            "to_phone": to_phone,
            "to_address": to_address,
            "to_ward_code": to_ward_code,
            "to_district_id": to_district_id,
            "cod_amount": cod_amount,
            "content": content,
            "weight": weight,
            "length": length,
            "width": width,
            "height": height,
            "service_type_id": service_type_id,
            "payment_type_id": payment_type_id,
            "required_note": required_note
        }

        # Thêm các tham số tùy chọn
        if client_order_code:
            data["client_order_code"] = client_order_code
        if insurance_value:
            data["insurance_value"] = insurance_value
        if coupon:
            data["coupon"] = coupon
        if pick_shift:
            data["pick_shift"] = pick_shift

        try:
            result = await make_ghn_request(
                "shiip/public-api/v2/shipping-order/create",
                method="POST",
                data=data
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi tạo đơn hàng: {str(e)}"

    @mcp.tool()
    async def cancel_order(order_code: str) -> str:
        """
        Hủy đơn hàng trên hệ thống GHN

        Args:
            order_code: Mã đơn hàng GHN cần hủy

        Returns:
            JSON string chứa kết quả hủy đơn
        """
        data = {"order_codes": [order_code]}

        try:
            result = await make_ghn_request(
                "shiip/public-api/v2/switch-status/cancel",
                method="POST",
                data=data
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi hủy đơn hàng: {str(e)}"

    @mcp.tool()
    async def preview_order(
        to_name: str,
        to_phone: str,
        to_address: str,
        to_ward_code: str,
        to_district_id: int,
        cod_amount: int,
        content: str,
        weight: int,
        length: int,
        width: int,
        height: int,
        service_type_id: int,
        payment_type_id: int = 2,
        required_note: str = "KHONGCHOXEMHANG",
        insurance_value: Optional[int] = None
    ) -> str:
        """
        Xem trước đơn hàng (không tạo đơn thực)

        Args:
            to_name: Tên người nhận
            to_phone: Số điện thoại người nhận
            to_address: Địa chỉ người nhận
            to_ward_code: Mã phường/xã người nhận
            to_district_id: ID quận/huyện người nhận
            cod_amount: Số tiền thu hộ (COD)
            content: Nội dung hàng hóa
            weight: Khối lượng (gram)
            length: Chiều dài (cm)
            width: Chiều rộng (cm)
            height: Chiều cao (cm)
            service_type_id: ID loại dịch vụ
            payment_type_id: Hình thức thanh toán
            required_note: Ghi chú bắt buộc
            insurance_value: Giá trị bảo hiểm (tùy chọn)

        Returns:
            JSON string chứa thông tin xem trước đơn hàng
        """
        data = {
            "to_name": to_name,
            "to_phone": to_phone,
            "to_address": to_address,
            "to_ward_code": to_ward_code,
            "to_district_id": to_district_id,
            "cod_amount": cod_amount,
            "content": content,
            "weight": weight,
            "length": length,
            "width": width,
            "height": height,
            "service_type_id": service_type_id,
            "payment_type_id": payment_type_id,
            "required_note": required_note
        }

        if insurance_value:
            data["insurance_value"] = insurance_value

        try:
            result = await make_ghn_request(
                "shiip/public-api/v2/shipping-order/preview",
                method="POST",
                data=data
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi xem trước đơn hàng: {str(e)}"

    @mcp.tool()
    async def create_print_token(order_codes: List[str]) -> str:
        """
        Tạo token để in vận đơn

        Args:
            order_codes: Danh sách mã đơn GHN cần in vận đơn

        Returns:
            JSON string chứa token in vận đơn
        """
        data = {"order_codes": order_codes}

        try:
            result = await make_ghn_request(
                "shiip/public-api/v2/a5/gen-token",
                method="POST",
                data=data
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi tạo token in đơn hàng: {str(e)}"

    # CALCULATION TOOLS - Tính toán phí và thời gian
    @mcp.tool()
    async def calculate_fee(
        from_district_id: int,
        to_district_id: int,
        to_ward_code: str,
        weight: int,
        service_type_id: int,
        insurance_value: Optional[int] = None,
        coupon: Optional[str] = None,
        height: Optional[int] = None,
        length: Optional[int] = None,
        width: Optional[int] = None
    ) -> str:
        """
        Tính cước phí vận chuyển

        Args:
            from_district_id: ID quận/huyện lấy hàng
            to_district_id: ID quận/huyện giao hàng
            to_ward_code: Mã phường/xã giao hàng
            weight: Khối lượng (gram)
            service_type_id: ID loại dịch vụ
            insurance_value: Giá trị bảo hiểm (tùy chọn)
            coupon: Mã giảm giá (tùy chọn)
            height: Chiều cao (cm, tùy chọn)
            length: Chiều dài (cm, tùy chọn)
            width: Chiều rộng (cm, tùy chọn)

        Returns:
            JSON string chứa thông tin cước phí
        """
        data = {
            "service_type_id": service_type_id,
            "from_district_id": from_district_id,
            "to_district_id": to_district_id,
            "to_ward_code": to_ward_code,
            "weight": weight
        }

        # Thêm các tham số tùy chọn
        if insurance_value:
            data["insurance_value"] = insurance_value
        if coupon:
            data["coupon"] = coupon
        if height:
            data["height"] = height
        if length:
            data["length"] = length
        if width:
            data["width"] = width

        try:
            result = await make_ghn_request(
                "shiip/public-api/v2/shipping-order/fee",
                method="POST",
                data=data
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi tính cước phí: {str(e)}"

    @mcp.tool()
    async def calculate_leadtime(
        from_district_id: int,
        to_district_id: int,
        to_ward_code: str,
        service_id: int,
        from_ward_code: Optional[str] = None
    ) -> str:
        """
        Tính thời gian giao hàng dự kiến

        Args:
            from_district_id: ID quận/huyện lấy hàng
            to_district_id: ID quận/huyện giao hàng
            to_ward_code: Mã phường/xã giao hàng
            service_id: ID dịch vụ GHN
            from_ward_code: Mã phường/xã lấy hàng (tùy chọn)

        Returns:
            JSON string chứa thông tin thời gian giao dự kiến
        """
        data = {
            "from_district_id": from_district_id,
            "to_district_id": to_district_id,
            "to_ward_code": to_ward_code,
            "service_id": service_id
        }

        if from_ward_code:
            data["from_ward_code"] = from_ward_code

        try:
            result = await make_ghn_request(
                "shiip/public-api/v2/shipping-order/leadtime",
                method="POST",
                data=data
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi tính thời gian giao hàng: {str(e)}"

    # SUPPORT TOOLS - Hỗ trợ khách hàng
    @mcp.tool()
    async def create_ticket(
        order_code: str,
        category: str,
        description: str,
        c_email: Optional[str] = None
    ) -> str:
        """
        Tạo ticket hỗ trợ

        Args:
            order_code: Mã đơn GHN liên quan đến ticket
            category: Chủ đề yêu cầu (Tư vấn, Hối Giao/Lấy/Trả hàng, Thay đổi thông tin, Khiếu nại)
            description: Nội dung yêu cầu
            c_email: Email khách hàng (tùy chọn)

        Returns:
            JSON string chứa thông tin ticket đã tạo
        """
        data = {
            "order_code": order_code,
            "category": category,
            "description": description
        }

        if c_email:
            data["c_email"] = c_email

        try:
            result = await make_ghn_request(
                "public-api/ticket/create",
                method="POST",
                data=data
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi tạo ticket: {str(e)}"

    @mcp.tool()
    async def reply_ticket(ticket_id: int, description: str) -> str:
        """
        Phản hồi ticket hỗ trợ

        Args:
            ticket_id: ID của ticket
            description: Nội dung phản hồi

        Returns:
            JSON string chứa kết quả phản hồi
        """
        data = {
            "ticket_id": ticket_id,
            "description": description
        }

        try:
            result = await make_ghn_request(
                "public-api/ticket/reply",
                method="POST",
                data=data
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi phản hồi ticket: {str(e)}"

    @mcp.tool()
    async def get_otp(phone: str) -> str:
        """
        Lấy mã OTP để đăng ký hoặc thêm cửa hàng

        Args:
            phone: Số điện thoại của chủ cửa hàng đối tác

        Returns:
            JSON string chứa thông tin OTP
        """
        data = {"phone": phone}

        try:
            result = await make_ghn_request(
                "shiip/public-api/v2/shop/affiliateOTP",
                method="POST",
                data=data
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy OTP: {str(e)}"

    # MANAGEMENT TOOLS - Quản lý cửa hàng
    @mcp.tool()
    async def add_staff_to_store(phone: str, otp: str, shop_id: int) -> str:
        """
        Thêm nhân viên vào cửa hàng qua OTP

        Args:
            phone: Số điện thoại của khách hàng đối tác
            otp: Mã OTP nhận được từ API Lấy OTP
            shop_id: ID cửa hàng

        Returns:
            JSON string chứa kết quả thêm nhân viên
        """
        data = {
            "phone": phone,
            "otp": otp,
            "shop_id": shop_id
        }

        try:
            result = await make_ghn_request(
                "shiip/public-api/v2/shop/affiliateCreateWithShop",
                method="POST",
                data=data
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi thêm nhân viên vào cửa hàng: {str(e)}"
