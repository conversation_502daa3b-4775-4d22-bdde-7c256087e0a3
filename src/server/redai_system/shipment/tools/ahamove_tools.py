"""
Ahamove Tools cho Shipment MCP Server

Module này chứa các tools để tương tác với Ahamove API
"""

import json
from typing import Dict, Any, List
from mcp.server.fastmcp import FastMCP

# Import với fallback
try:
    from src.server.redai_system.utils.ahamove_client import (
        ahamove_register_account,
        ahamove_authenticate_token,
        ahamove_add_child_account,
        ahamove_activate_child_account,
        ahamove_remove_child_account,
        ahamove_get_child_accounts,
        ahamove_update_profile,
        ahamove_get_cities,
        ahamove_get_services,
        ahamove_get_service_details,
        ahamove_estimate_order_fee,
        ahamove_create_order,
        ahamove_get_order_detail,
        ahamove_cancel_order,
        ahamove_get_order_list,
        ahamove_get_order_tracking
    )
except ImportError:
    from utils.ahamove_client import (
        ahamove_register_account,
        ahamove_authenticate_token,
        ahamove_add_child_account,
        ahamove_activate_child_account,
        ahamove_remove_child_account,
        ahamove_get_child_accounts,
        ahamove_update_profile,
        ahamove_get_cities,
        ahamove_get_services,
        ahamove_get_service_details,
        ahamove_estimate_order_fee,
        ahamove_create_order,
        ahamove_get_order_detail,
        ahamove_cancel_order,
        ahamove_get_order_list,
        ahamove_get_order_tracking
    )

def register_ahamove_tools(mcp: FastMCP):
    """Đăng ký tất cả Ahamove tools với MCP server"""

    @mcp.tool()
    async def ahamove_register_account_tool(
        mobile: str,
        name: str,
        address: str
    ) -> str:
        """
        Đăng ký tài khoản Ahamove mới
        
        Args:
            mobile: Số điện thoại đăng ký (format: 84xxxxxxxxx)
            name: Tên đối tác
            address: Địa chỉ đối tác
        
        Returns:
            JSON string chứa token và refresh_token
        """
        try:
            result = await ahamove_register_account(mobile, name, address)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi đăng ký tài khoản Ahamove: {str(e)}"

    @mcp.tool()
    async def ahamove_authenticate_token_tool(mobile: str) -> str:
        """
        Xác thực và lấy token mới cho tài khoản đã có
        
        Args:
            mobile: Số điện thoại đã đăng ký
        
        Returns:
            JSON string chứa token và refresh_token mới
        """
        try:
            result = await ahamove_authenticate_token(mobile)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi xác thực token Ahamove: {str(e)}"

    @mcp.tool()
    async def ahamove_add_child_account_tool(child_id: str) -> str:
        """
        Thêm tài khoản con vào tài khoản cha
        
        Args:
            child_id: Số điện thoại của tài khoản con
        
        Returns:
            JSON string chứa kết quả (sẽ gửi SMS xác thực)
        """
        try:
            result = await ahamove_add_child_account(child_id)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi thêm tài khoản con Ahamove: {str(e)}"

    @mcp.tool()
    async def ahamove_activate_child_account_tool(
        child_id: str,
        activation_code: str
    ) -> str:
        """
        Kích hoạt tài khoản con bằng mã xác thực SMS
        
        Args:
            child_id: Số điện thoại của tài khoản con
            activation_code: Mã kích hoạt nhận được qua SMS
        
        Returns:
            JSON string chứa thông tin tài khoản con đã kích hoạt
        """
        try:
            result = await ahamove_activate_child_account(child_id, activation_code)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi kích hoạt tài khoản con Ahamove: {str(e)}"

    @mcp.tool()
    async def ahamove_remove_child_account_tool(child_id: str) -> str:
        """
        Xóa tài khoản con khỏi tài khoản cha
        
        Args:
            child_id: Số điện thoại của tài khoản con cần xóa
        
        Returns:
            JSON string chứa kết quả xóa
        """
        try:
            result = await ahamove_remove_child_account(child_id)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi xóa tài khoản con Ahamove: {str(e)}"

    @mcp.tool()
    async def ahamove_get_child_accounts_tool() -> str:
        """
        Lấy danh sách tất cả tài khoản con
        
        Returns:
            JSON string chứa danh sách tài khoản con
        """
        try:
            result = await ahamove_get_child_accounts()
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách tài khoản con Ahamove: {str(e)}"

    @mcp.tool()
    async def ahamove_update_profile_tool(
        name: str = "",
        email: str = ""
    ) -> str:
        """
        Cập nhật thông tin tài khoản
        
        Args:
            name: Tên mới (để trống nếu không cập nhật)
            email: Email mới (để trống nếu không cập nhật)
        
        Returns:
            JSON string chứa kết quả cập nhật
        """
        try:
            result = await ahamove_update_profile(
                name if name else None,
                email if email else None
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi cập nhật thông tin Ahamove: {str(e)}"

    @mcp.tool()
    async def ahamove_get_cities_tool(country_id: str = "VN") -> str:
        """
        Lấy danh sách thành phố Ahamove hỗ trợ
        
        Args:
            country_id: Mã quốc gia (mặc định VN)
        
        Returns:
            JSON string chứa danh sách thành phố
        """
        try:
            result = await ahamove_get_cities(country_id)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách thành phố Ahamove: {str(e)}"

    @mcp.tool()
    async def ahamove_get_services_tool(
        city_id: str = "",
        lat: str = "",
        lng: str = "",
        delivery_type: str = ""
    ) -> str:
        """
        Lấy danh sách dịch vụ Ahamove
        
        Args:
            city_id: Mã thành phố (ví dụ: SGN, HAN)
            lat: Vĩ độ (nếu không dùng city_id)
            lng: Kinh độ (nếu không dùng city_id)
            delivery_type: Loại giao hàng (INSTANT, TRUCK, ...)
        
        Returns:
            JSON string chứa danh sách dịch vụ và yêu cầu đặc biệt
        """
        try:
            result = await ahamove_get_services(
                city_id if city_id else None,
                lat if lat else None,
                lng if lng else None,
                delivery_type if delivery_type else None
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách dịch vụ Ahamove: {str(e)}"

    @mcp.tool()
    async def ahamove_get_service_details_tool(service_id: str) -> str:
        """
        Lấy chi tiết dịch vụ Ahamove
        
        Args:
            service_id: Mã dịch vụ (ví dụ: SGN-BIKE)
        
        Returns:
            JSON string chứa chi tiết dịch vụ
        """
        try:
            result = await ahamove_get_service_details(service_id)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy chi tiết dịch vụ Ahamove: {str(e)}"

    @mcp.tool()
    async def ahamove_estimate_order_fee_tool(
        order_time: int,
        path: List[Dict[str, Any]],
        services: List[Dict[str, Any]],
        payment_method: str,
        remarks: str = "",
        promo_code: str = ""
    ) -> str:
        """
        Ước tính phí đơn hàng Ahamove
        
        Args:
            order_time: Thời gian giao (0 = ngay, timestamp = đặt trước)
            path: Danh sách điểm (ít nhất 2), mỗi điểm có lat, lng, address
            services: Danh sách dịch vụ để so sánh, mỗi dịch vụ có _id và requests
            payment_method: Phương thức thanh toán (CASH, BALANCE, CASH_BY_RECIPIENT)
            remarks: Ghi chú đơn hàng
            promo_code: Mã khuyến mãi
        
        Returns:
            JSON string chứa ước tính phí cho từng dịch vụ
        """
        try:
            order_data = {
                "order_time": order_time,
                "path": path,
                "services": services,
                "payment_method": payment_method
            }
            
            if remarks:
                order_data["remarks"] = remarks
            if promo_code:
                order_data["promo_code"] = promo_code
            
            result = await ahamove_estimate_order_fee(order_data)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi ước tính phí Ahamove: {str(e)}"

    @mcp.tool()
    async def ahamove_create_order_tool(
        order_time: int,
        path: List[Dict[str, Any]],
        service_id: str,
        requests: List[Dict[str, Any]],
        payment_method: str,
        remarks: str = "",
        promo_code: str = "",
        route_optimized: bool = False
    ) -> str:
        """
        Tạo đơn hàng Ahamove mới
        
        Args:
            order_time: Thời gian giao (0 = ngay, timestamp = đặt trước)
            path: Danh sách điểm, mỗi điểm có lat, lng, address, name, mobile, cod, etc.
            service_id: Mã dịch vụ (ví dụ: SGN-BIKE)
            requests: Danh sách yêu cầu đặc biệt
            payment_method: Phương thức thanh toán
            remarks: Ghi chú đơn hàng
            promo_code: Mã khuyến mãi
            route_optimized: Tối ưu lộ trình (cho đơn nhiều điểm)
        
        Returns:
            JSON string chứa thông tin đơn hàng đã tạo
        """
        try:
            order_data = {
                "order_time": order_time,
                "path": path,
                "service_id": service_id,
                "requests": requests,
                "payment_method": payment_method
            }
            
            if remarks:
                order_data["remarks"] = remarks
            if promo_code:
                order_data["promo_code"] = promo_code
            if route_optimized:
                order_data["route_optimized"] = route_optimized
            
            result = await ahamove_create_order(order_data)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi tạo đơn hàng Ahamove: {str(e)}"

    @mcp.tool()
    async def ahamove_get_order_detail_tool(order_id: str) -> str:
        """
        Lấy chi tiết đơn hàng Ahamove
        
        Args:
            order_id: Mã đơn hàng Ahamove
        
        Returns:
            JSON string chứa chi tiết đơn hàng
        """
        try:
            result = await ahamove_get_order_detail(order_id)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy chi tiết đơn hàng Ahamove: {str(e)}"

    @mcp.tool()
    async def ahamove_cancel_order_tool(order_id: str, comment: str = "") -> str:
        """
        Hủy đơn hàng Ahamove
        
        Args:
            order_id: Mã đơn hàng cần hủy
            comment: Lý do hủy đơn
        
        Returns:
            JSON string chứa kết quả hủy đơn
        """
        try:
            result = await ahamove_cancel_order(order_id, comment)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi hủy đơn hàng Ahamove: {str(e)}"

    @mcp.tool()
    async def ahamove_get_order_list_tool(
        offset: int = 0,
        limit: int = 50,
        statuses: str = "",
        from_date: str = "",
        to_date: str = ""
    ) -> str:
        """
        Lấy danh sách đơn hàng Ahamove
        
        Args:
            offset: Vị trí bắt đầu (phân trang)
            limit: Số lượng đơn tối đa (tối đa 50)
            statuses: Danh sách trạng thái cách nhau bởi dấu phẩy
            from_date: Ngày bắt đầu (YYYY-MM-DD)
            to_date: Ngày kết thúc (YYYY-MM-DD)
        
        Returns:
            JSON string chứa danh sách đơn hàng
        """
        try:
            status_list = statuses.split(",") if statuses else None
            result = await ahamove_get_order_list(
                offset, limit, status_list, 
                from_date if from_date else None,
                to_date if to_date else None
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách đơn hàng Ahamove: {str(e)}"

    @mcp.tool()
    async def ahamove_get_order_tracking_tool(order_id: str) -> str:
        """
        Lấy thông tin tracking đơn hàng Ahamove
        
        Args:
            order_id: Mã đơn hàng Ahamove
        
        Returns:
            JSON string chứa thông tin tracking chi tiết
        """
        try:
            result = await ahamove_get_order_tracking(order_id)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy tracking đơn hàng Ahamove: {str(e)}"
