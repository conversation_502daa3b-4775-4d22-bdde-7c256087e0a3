"""
GHTK Tools cho Shipment MCP Server

Module này chứa các tools để tương tác với GHTK API
"""

import json
from typing import Dict, Any, List
from mcp.server.fastmcp import FastMCP

# Import với fallback
try:
    from src.server.redai_system.utils.ghtk_client import (
        ghtk_get_solutions,
        ghtk_create_order,
        ghtk_calculate_fee,
        ghtk_get_order_status,
        ghtk_print_label,
        ghtk_cancel_order,
        ghtk_get_pick_addresses,
        ghtk_get_level4_addresses,
        ghtk_search_products
    )
except ImportError:
    from utils.ghtk_client import (
        ghtk_get_solutions,
        ghtk_create_order,
        ghtk_calculate_fee,
        ghtk_get_order_status,
        ghtk_print_label,
        ghtk_cancel_order,
        ghtk_get_pick_addresses,
        ghtk_get_level4_addresses,
        ghtk_search_products
    )

def register_ghtk_tools(mcp: FastMCP):
    """Đăng ký tất cả GHTK tools với MCP server"""

    @mcp.tool()
    async def ghtk_get_solutions_tool() -> str:
        """
        <PERSON><PERSON><PERSON> danh sách giải pháp (g<PERSON><PERSON> b<PERSON><PERSON> hiểm/phụ phí) từ GHTK
        
        Returns:
            JSON string chứa danh sách giải pháp
        """
        try:
            result = await ghtk_get_solutions()
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách giải pháp GHTK: {str(e)}"

    @mcp.tool()
    async def ghtk_create_order_tool(
        products: List[Dict[str, Any]],
        order_info: Dict[str, Any]
    ) -> str:
        """
        Tạo đơn hàng mới trên GHTK
        
        Args:
            products: Danh sách sản phẩm trong đơn hàng
                Mỗi sản phẩm cần có: name (bắt buộc), weight (bắt buộc), price, quantity, product_code
            order_info: Thông tin đơn hàng
                Cần có: id, pick_name, pick_address, pick_province, pick_district, pick_ward, pick_tel,
                       name, address, province, district, ward, tel, value
                Tùy chọn: hamlet, is_freeship, pick_date, pick_money, note, transport, pick_option, deliver_option, tags, sub_tags
        
        Returns:
            JSON string chứa thông tin đơn hàng đã tạo
        """
        try:
            order_data = {
                "products": products,
                "order": order_info
            }
            result = await ghtk_create_order(order_data)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi tạo đơn hàng GHTK: {str(e)}"

    @mcp.tool()
    async def ghtk_calculate_fee_tool(
        pick_province: str,
        pick_district: str,
        province: str,
        district: str,
        weight: int,
        pick_address_id: str = "",
        pick_address: str = "",
        pick_ward: str = "",
        pick_street: str = "",
        address: str = "",
        ward: str = "",
        street: str = "",
        value: int = 0,
        transport: str = "",
        deliver_option: str = "none",
        tags: List[int] = None
    ) -> str:
        """
        Tính phí vận chuyển GHTK
        
        Args:
            pick_province: Tỉnh/thành nơi lấy hàng (bắt buộc)
            pick_district: Quận/huyện lấy hàng (bắt buộc)
            province: Tỉnh/thành người nhận (bắt buộc)
            district: Quận/huyện người nhận (bắt buộc)
            weight: Tổng cân nặng (gram) (bắt buộc)
            pick_address_id: Mã địa chỉ lấy hàng (nếu có)
            pick_address: Địa chỉ lấy hàng chi tiết
            pick_ward: Phường/xã lấy hàng
            pick_street: Đường/phố lấy hàng
            address: Địa chỉ nhận hàng chi tiết
            ward: Phường/xã người nhận
            street: Đường/phố người nhận
            value: Giá trị đơn hàng (VNĐ) để tính phí khai giá
            transport: Phương thức vận chuyển (road/fly)
            deliver_option: Dịch vụ giao hàng (xteam/none)
            tags: Mảng mã nhãn áp dụng
        
        Returns:
            JSON string chứa thông tin phí vận chuyển
        """
        try:
            fee_params = {
                "pick_province": pick_province,
                "pick_district": pick_district,
                "province": province,
                "district": district,
                "weight": weight
            }
            
            # Thêm các tham số tùy chọn
            if pick_address_id:
                fee_params["pick_address_id"] = pick_address_id
            if pick_address:
                fee_params["pick_address"] = pick_address
            if pick_ward:
                fee_params["pick_ward"] = pick_ward
            if pick_street:
                fee_params["pick_street"] = pick_street
            if address:
                fee_params["address"] = address
            if ward:
                fee_params["ward"] = ward
            if street:
                fee_params["street"] = street
            if value > 0:
                fee_params["value"] = value
            if transport:
                fee_params["transport"] = transport
            if deliver_option:
                fee_params["deliver_option"] = deliver_option
            if tags:
                fee_params["tags"] = tags
            
            result = await ghtk_calculate_fee(fee_params)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi tính phí vận chuyển GHTK: {str(e)}"

    @mcp.tool()
    async def ghtk_get_order_status_tool(tracking_order: str) -> str:
        """
        Lấy trạng thái đơn hàng GHTK
        
        Args:
            tracking_order: Mã vận đơn GHTK hoặc mã đơn đối tác
        
        Returns:
            JSON string chứa thông tin trạng thái đơn hàng
        """
        try:
            result = await ghtk_get_order_status(tracking_order)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy trạng thái đơn hàng GHTK: {str(e)}"

    @mcp.tool()
    async def ghtk_print_label_tool(
        tracking_order: str,
        original: str = "portrait",
        paper_size: str = "A6"
    ) -> str:
        """
        In nhãn đơn hàng GHTK
        
        Args:
            tracking_order: Mã vận đơn GHTK
            original: Kiểu in (portrait/landscape)
            paper_size: Kích thước giấy (A5/A6)
        
        Returns:
            JSON string chứa thông tin file PDF nhãn
        """
        try:
            result = await ghtk_print_label(tracking_order, original, paper_size)
            
            if result.get("content_type") == "application/pdf":
                # Trả về thông tin về file PDF
                return json.dumps({
                    "success": True,
                    "message": "Nhãn đơn hàng đã được tạo thành công",
                    "content_type": "application/pdf",
                    "filename": result.get("filename", f"label_{tracking_order}.pdf"),
                    "size_bytes": len(result.get("content", b""))
                }, ensure_ascii=False, indent=2)
            else:
                return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi in nhãn đơn hàng GHTK: {str(e)}"

    @mcp.tool()
    async def ghtk_cancel_order_tool(tracking_order: str) -> str:
        """
        Hủy đơn hàng GHTK
        
        Args:
            tracking_order: Mã vận đơn GHTK hoặc partner_id:{PARTNER_CODE} cho mã đối tác
        
        Returns:
            JSON string chứa kết quả hủy đơn
        """
        try:
            result = await ghtk_cancel_order(tracking_order)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi hủy đơn hàng GHTK: {str(e)}"

    @mcp.tool()
    async def ghtk_get_pick_addresses_tool() -> str:
        """
        Lấy danh sách địa chỉ lấy hàng (kho) đã cấu hình trên GHTK
        
        Returns:
            JSON string chứa danh sách địa chỉ lấy hàng
        """
        try:
            result = await ghtk_get_pick_addresses()
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách địa chỉ lấy hàng GHTK: {str(e)}"

    @mcp.tool()
    async def ghtk_get_level4_addresses_tool(
        province: str,
        district: str,
        ward_street: str,
        address: str = ""
    ) -> str:
        """
        Lấy danh sách địa chỉ cấp 4 (tòa nhà, ngõ, xóm) tại một khu vực cụ thể
        
        Args:
            province: Tỉnh/thành phố (bắt buộc)
            district: Quận/huyện (bắt buộc)
            ward_street: Phường/xã hoặc đường (bắt buộc)
            address: Địa chỉ chi tiết để tìm kiếm (tùy chọn)
        
        Returns:
            JSON string chứa danh sách địa chỉ cấp 4
        """
        try:
            result = await ghtk_get_level4_addresses(province, district, ward_street, address)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách địa chỉ cấp 4 GHTK: {str(e)}"

    @mcp.tool()
    async def ghtk_search_products_tool(term: str) -> str:
        """
        Tìm kiếm sản phẩm đã tạo trên hệ thống GHTK
        
        Args:
            term: Từ khóa tên sản phẩm cần tìm
        
        Returns:
            JSON string chứa danh sách sản phẩm tìm được
        """
        try:
            result = await ghtk_search_products(term)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi tìm kiếm sản phẩm GHTK: {str(e)}"
