# RedAI Data Module MCP Server - Thiết kế và Kiến trúc

## Tổng quan

RedAI Data Module MCP Server được thiết kế dựa trên FastMCP framework để cung cấp giao diện MCP cho Data Module API. Server tự động tạo tools và resources từ OpenAPI specification và hỗ trợ authentication động từ client.

## Kiến trúc

### 1. Cấu trúc Files

```
src/server/redai_system/
├── data_module_server.py           # Server chính
├── data_module_config.py           # Cấu hình và utilities  
├── example_data_module_client.py   # Ví dụ sử dụng client
├── test_data_module_server.py      # Test script
└── data/
    ├── swagger.json               # OpenAPI specification
    └── README.md                  # Documentation
```

### 2. Thành phần chính

#### DataModuleServer Class
- **Mục đích**: Quản lý việc tạo MCP server với authentication động
- **Chứ<PERSON> năng**:
  - Load OpenAPI schema từ `data/swagger.json`
  - Tạo HTTP client với Bearer token
  - Tùy chỉnh MCP components (tools, descriptions, emojis)
  - Thêm authentication tools

#### Authentication Tools
- **update_bearer_token**: Cập nhật Bearer token từ client
- **check_auth_status**: Kiểm tra trạng thái authentication

#### API Tools (tự động tạo từ OpenAPI)
- **Media Tools**: Quản lý media files
- **URL Tools**: Quản lý URL resources
- **Statistics Tools**: Thống kê dữ liệu

## Đặc điểm thiết kế

### 1. Authentication từ Client

**Lý do thiết kế**:
- Bảo mật cao hơn (token không lưu trong server)
- Linh hoạt (mỗi client có token riêng)
- Dynamic (có thể thay đổi token runtime)

**Cách hoạt động**:
1. Client kết nối tới server (không cần token)
2. Client gọi `update_bearer_token` để cấu hình auth
3. Server tạo HTTP client mới với token
4. Các API calls tiếp theo sử dụng token này

### 2. Tự động tạo Tools từ OpenAPI

**FastMCP.from_openapi()** tự động:
- Parse OpenAPI specification
- Tạo tools cho mỗi endpoint
- Generate input schemas từ parameters
- Handle request/response mapping

**Customization**:
- Đổi tên tools dễ hiểu
- Thêm emoji và descriptions chi tiết
- Phân loại theo tags

### 3. Error Handling

**Levels**:
- **Server level**: Xử lý lỗi khởi tạo, schema loading
- **Tool level**: Xử lý lỗi API calls, authentication
- **Client level**: Xử lý lỗi connection, timeout

**Strategies**:
- Graceful degradation
- Detailed error messages
- Logging và monitoring

## Cấu hình

### Environment Variables

```bash
# API Configuration
REDAI_DATA_API_BASE_URL="https://api.redai.com"

# Server Configuration
DATA_MODULE_HTTP_HOST="127.0.0.1"
DATA_MODULE_HTTP_PORT="8002"
DATA_MODULE_HTTP_PATH="/mcp"
```

### Runtime Configuration

- Bearer token: Từ client via `update_bearer_token` tool
- HTTP timeout: 30 seconds
- Transport: Streamable HTTP

## API Mapping

### OpenAPI → MCP Tools

| OpenAPI Endpoint | MCP Tool Name | Mô tả |
|------------------|---------------|-------|
| `GET /media/my-media` | `get_my_media_list` | Lấy danh sách media |
| `DELETE /media/my-media` | `delete_my_media_batch` | Xóa nhiều media |
| `GET /data/url` | `get_my_urls` | Lấy danh sách URLs |
| `POST /data/url` | `create_new_url` | Tạo URL mới |
| `GET /data/url/{id}` | `get_url_details` | Chi tiết URL |
| `PUT /data/url/{id}` | `update_url` | Cập nhật URL |
| `DELETE /data/url/{id}` | `delete_url` | Xóa URL |
| `GET /statistics` | `get_user_statistics` | Thống kê user |
| `GET /statistics/storage` | `get_storage_statistics` | Thống kê storage |

### Tool Categories

- 🔐 **Authentication**: `update_bearer_token`, `check_auth_status`
- 🖼️ **Media**: `get_my_media_list`, `delete_my_media_batch`
- 🔗 **URL**: `get_my_urls`, `create_new_url`, `get_url_details`, `update_url`, `delete_url`
- 📊 **Statistics**: `get_user_statistics`, `get_storage_statistics`

## Sử dụng

### 1. Khởi động Server

```bash
python data_module_server.py
```

### 2. Kết nối từ Client

```python
from fastmcp import Client

async with Client("http://127.0.0.1:8002/mcp") as client:
    # Cấu hình auth
    await client.call_tool("update_bearer_token", {
        "bearer_token": "your_token"
    })
    
    # Sử dụng API
    result = await client.call_tool("get_my_media_list", {
        "page": 1, "limit": 10
    })
```

### 3. Testing

```bash
python test_data_module_server.py
python example_data_module_client.py
```

## Mở rộng

### Thêm API mới

1. Cập nhật `data/swagger.json` với endpoint mới
2. Server tự động tạo tool tương ứng
3. Tùy chỉnh tool name trong `customize_component_names()`
4. Thêm emoji/description trong `customize_mcp_components()`

### Thêm Authentication method

1. Extend `DataModuleServer` class
2. Thêm method tạo client với auth type mới
3. Thêm tool để cấu hình auth mới

### Custom Tools

```python
@mcp_server.tool
async def custom_tool(param: str, ctx: Context) -> str:
    """Custom tool description"""
    # Implementation
    return result
```

## Best Practices

### 1. Security
- Không log Bearer tokens
- Validate input parameters
- Handle authentication errors gracefully

### 2. Performance
- Reuse HTTP clients
- Implement connection pooling
- Cache OpenAPI schema

### 3. Monitoring
- Log API call metrics
- Track authentication status
- Monitor error rates

### 4. Testing
- Unit tests cho từng component
- Integration tests với real API
- Mock tests cho development

## Troubleshooting

### Common Issues

1. **Schema loading error**
   - Kiểm tra file `data/swagger.json` tồn tại
   - Validate JSON syntax

2. **Authentication failed**
   - Kiểm tra Bearer token hợp lệ
   - Verify API base URL

3. **Connection refused**
   - Kiểm tra server đang chạy
   - Verify host/port configuration

4. **Tool not found**
   - Kiểm tra OpenAPI spec có endpoint
   - Verify tool name mapping

### Debug Mode

```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Test individual components
mcp_server = data_module_server.create_server_with_auth("test_token")
```

## Roadmap

### Phase 1 (Hiện tại)
- ✅ Basic MCP server với OpenAPI integration
- ✅ Authentication từ client
- ✅ Core API tools

### Phase 2 (Tương lai)
- 🔄 Advanced error handling
- 🔄 Caching và performance optimization
- 🔄 Monitoring và metrics

### Phase 3 (Mở rộng)
- 🔄 Multiple authentication methods
- 🔄 Custom business logic tools
- 🔄 Real-time notifications
